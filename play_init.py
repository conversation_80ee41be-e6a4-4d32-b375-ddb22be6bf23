"""""
加载配置: 从 YAML 配置文件 (config_play.yaml) 加载程序运行所需的参数。
配置日志: 设置日志记录器，配置日志级别、格式，并将日志同时输出到控制台和文件。
生成玩家ID: 根据平台和昵称生成唯一的哈希值作为玩家ID。
解析消息: 从接收到的消息对象中解析出平台、时间、昵称、玩家ID、内容和头像等字段。
设置信号处理: 设置信号处理函数，以便在接收到终止信号（如 Ctrl+C）时，安全地停止程序。
检查会话: 检查是否存在有效的历史会话，并询问用户是否继续使用该会话，否则创建新的会话。
加载会话数据: 从数据库加载会话相关的玩家信息、评论、游戏记录和队列数据。
初始化会话: 初始化会话，包括创建或加载会话数据，初始化 OBS 控制器和数据库同步管理器。
主循环监控: 启动一个监控线程，定期检查主循环的运行状态，并在主循环卡住时发出警告。
订单抓取: 启动一个订单抓取线程，定期从指定来源抓取订单信息，并更新到数据库。
"""""
# --- 关键修改：导入 MutableMapping ---
from typing import Dict, List, Tuple, Any, Optional, Union, cast, TypeVar, MutableMapping
from threading import Lock, RLock
import yaml
import logging
import hashlib
import signal
import sys
import os
import traceback
import threading
import copy
import time
from datetime import datetime, timedelta

import Play_db
import Play_obs
import play_db_sync
import play_processing
from play_processing import _recalculate_and_update_comments_after_game

logger: Optional[logging.Logger] = None

def get_local_path(filename):
    """获取本地文件路径，支持打包后的exe和脚本运行"""
    # 检测是否为编译后的程序
    is_compiled = False

    # 多种检测方法
    if (hasattr(sys, '_MEIPASS') or  # PyInstaller
        getattr(sys, 'frozen', False) or  # 标准frozen检测
        sys.argv[0].endswith('.exe') or  # exe文件
        os.path.abspath(sys.executable) == os.path.abspath(sys.argv[0])):  # executable等于argv[0]
        is_compiled = True

    # 额外检查：如果__file__在临时目录，也认为是编译后的
    if not is_compiled and '__file__' in globals():
        file_path = os.path.abspath(__file__)
        if 'temp' in file_path.lower() or 'onefil' in file_path.lower():
            is_compiled = True

    if is_compiled:
        # 使用sys.executable的目录
        base_path = os.path.dirname(os.path.abspath(sys.executable))
    else:
        # 脚本模式，使用__file__
        base_path = os.path.dirname(os.path.abspath(__file__))

    return os.path.join(base_path, filename)

def load_config(config_file='config_play.yaml') -> Dict[str, Any]:
    """从配置文件加载参数"""
    global _config_file_mtime_cache

    try:
        config_path = get_local_path(config_file)
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)

        # 初始化配置文件修改时间缓存
        current_mtime = get_config_file_mtime(config_file)
        if current_mtime is not None:
            _config_file_mtime_cache[config_file] = current_mtime

        return config
    except FileNotFoundError:
        print(f"错误：找不到配置文件 {config_file}")
        sys.exit(1)
    except yaml.YAMLError:
        print(f"错误：YAML配置文件 {config_file} 格式不正确")
        sys.exit(1)
    except Exception as e:
        print(f"加载配置文件时发生未知错误: {e}")
        sys.exit(1)

# 配置重新加载锁，确保线程安全
_config_reload_lock = threading.Lock()

# 配置文件修改时间缓存
_config_file_mtime_cache = {}

def get_config_file_mtime(config_file='config_play.yaml') -> Optional[float]:
    """获取配置文件的修改时间"""
    try:
        config_path = get_local_path(config_file)
        return os.path.getmtime(config_path)
    except (OSError, FileNotFoundError):
        return None

def reload_specific_config_sections(config_file='config_play.yaml', force_reload=False) -> Optional[Dict[str, Any]]:
    """
    重新加载配置文件中的特定部分：game、obs（除enabled、网址、端口、密码外）、web_display（除enabled、端口外的所有参数）

    Args:
        config_file: 配置文件路径
        force_reload: 是否强制重新加载，忽略文件修改时间检查

    Returns:
        包含这三个部分的字典，如果无需更新或加载失败返回None
    """
    global logger, _config_file_mtime_cache

    with _config_reload_lock:
        try:
            # 检查文件修改时间
            current_mtime = get_config_file_mtime(config_file)
            if current_mtime is None:
                if logger:
                    logger.error(f"无法获取配置文件 {config_file} 的修改时间")
                return None

            # 如果不是强制重新加载，检查文件是否有修改
            if not force_reload:
                cached_mtime = _config_file_mtime_cache.get(config_file)
                if cached_mtime is not None and cached_mtime == current_mtime:
                    if logger:
                        logger.debug(f"配置文件 {config_file} 未发生修改，跳过重新加载")
                    return None

            if logger:
                logger.info(f"开始重新加载配置文件的特定部分... (修改时间: {current_mtime})")

            config_path = get_local_path(config_file)
            with open(config_path, 'r', encoding='utf-8') as f:
                new_config = yaml.safe_load(f)

            # 更新修改时间缓存
            _config_file_mtime_cache[config_file] = current_mtime

            # 提取需要更新的配置部分
            updated_sections = {}

            # 1. game 部分（完整加载）
            if 'game' in new_config:
                updated_sections['game'] = copy.deepcopy(new_config['game'])
                if logger:
                    logger.info(f"重新加载 game 配置部分")

            # 2. obs 部分（除了 enabled、host、port、password 外的所有参数）
            if 'obs' in new_config:
                obs_config = copy.deepcopy(new_config['obs'])
                # 移除不允许运行时更改的参数
                for key in ['enabled', 'host', 'port', 'password']:
                    if key in obs_config:
                        del obs_config[key]
                updated_sections['obs'] = obs_config
                if logger:
                    logger.info(f"重新加载 obs 配置部分（除连接参数外）")

            # 3. web_display 部分（除了 enabled、websocket_port 外的所有参数）
            if 'web_display' in new_config:
                web_display_config = copy.deepcopy(new_config['web_display'])
                # 移除不允许运行时更改的参数
                for key in ['enabled', 'websocket_port']:
                    if key in web_display_config:
                        del web_display_config[key]
                updated_sections['web_display'] = web_display_config
                if logger:
                    logger.info(f"重新加载 web_display 配置部分（除启用和端口参数外）")

            if logger:
                logger.info(f"配置重新加载完成，更新了 {len(updated_sections)} 个配置部分")

            return updated_sections

        except FileNotFoundError:
            if logger:
                logger.error(f"配置重新加载失败：找不到配置文件 {config_file}")
            return None
        except yaml.YAMLError as e:
            if logger:
                logger.error(f"配置重新加载失败：YAML格式错误 - {e}")
            return None
        except Exception as e:
            if logger:
                logger.error(f"配置重新加载时发生未知错误: {e}")
            return None

def setup_logging(config):
    """配置日志记录器，同时输出到控制台和文件"""
    global logger
    log_config = config.get('logging', {})
    log_level_str = log_config.get('level', 'INFO').upper()
    console_level_str = log_config.get('console_level', 'INFO').upper()
    file_level_str = log_config.get('file_level', 'INFO').upper()

    log_level = getattr(logging, log_level_str, logging.INFO)
    console_level = getattr(logging, console_level_str, logging.INFO)
    file_level = getattr(logging, file_level_str, logging.INFO)

    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
        handler.close()

    root_logger.setLevel(log_level)
    formatter = logging.Formatter('%(asctime)s - [%(threadName)s] - %(levelname)s - %(message)s')

    console_handler = logging.StreamHandler()
    console_handler.setLevel(console_level)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)

    try:
        file_handler = logging.FileHandler('play_main.log', encoding='utf-8')
        file_handler.setLevel(file_level)
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
    except Exception as e:
        print(f"无法创建日志文件 play_main.log: {e}")

    logger = logging.getLogger(__name__)
    
    # 设置其他模块的日志级别
    for module in ['Play_receiveMSG', 'Play_db', 'play_obs', 'request_guard', 'poll_request']:
        logging.getLogger(module).setLevel(log_level)

    if logger:
        logger.info("日志系统初始化完成")
    return logger

def generate_player_id(plat: str, name: str) -> str:
    """根据平台和昵称生成32位唯一哈希值作为玩家ID"""
    plat = plat or '抖音'
    name = name or '匿名用户'
    combined = f"{plat}:{name}"
    hash_obj = hashlib.sha256(combined.encode('utf-8'))
    return hash_obj.hexdigest()[:32]

def parse_message(message: Dict[str, Any], dt: str) -> Tuple[str, str, str, str, str, str]:
    """从消息对象中解析出所需字段"""
    plat = message.get('plat', '抖音')
    name = message.get('name', '')
    player = message.get('player', '')

    if not player and not name:
        return ('', '', '', '', '', '')

    if not player:
        player = generate_player_id(plat, name)

    return (plat, dt, name, player, message.get('content', ''), message.get('head_img', ''))

def setup_signal_handler(stop_flag: MutableMapping[str, bool], *args, **kwargs):
    """设置信号处理（Ctrl+C 退出），只做 stop_flag 标志置位"""
    def signal_handler(sig, frame):
        if stop_flag.get('running', True):
            if logger:
                logger.info("收到终止信号，标志置 False，主线程将退出并在 finally 做清理")
            stop_flag['running'] = False

    signal.signal(signal.SIGINT, signal_handler)
    if logger:
        logger.info("信号处理器设置完成（仅置标志）")

def check_previous_session(config: Dict[str, Any]) -> Optional[int]:
    """检查是否有有效的上次会话，并询问用户是否继续"""
    if logger:
        logger.info("检查历史会话记录...")
    
    # 确保数据库模块使用正确的配置
    Play_db.set_config(config)
    
    session_info = Play_db.get_session_info_batch()
    last_session = session_info['last_session']
    
    if not last_session:
        if logger:
            logger.info("未找到历史会话记录，将创建新会话")
        return Play_db.create_new_session()

    session_id = last_session['session_id']
    session_expire_hours = config.get('database', {}).get('session_expire_hours', 24)

    try:
        start_time = datetime.fromisoformat(last_session['start_time'])
        expire_time = start_time + timedelta(hours=session_expire_hours)
        session_valid = datetime.now() <= expire_time
    except ValueError:
        if logger:
            logger.error(f"解析会话开始时间出错: {last_session['start_time']}")
        session_valid = False

    if not session_valid:
        if logger:
            logger.info(f"上次会话(ID:{session_id})已过期，将创建新会话")
        if last_session['status'] == 'active':
            Play_db.close_session(session_id)
        return Play_db.create_new_session()

    # 显示会话信息
    stats = session_info['stats']
    recent_comments = session_info['recent_comments']
    queue_items = session_info['queue_items']

    if logger:
        logger.info("\n" + "="*50)
        logger.info(f"发现有效的历史会话(ID:{session_id})")
        logger.info(f"开始时间: {last_session['start_time']}")
        logger.info(f"状态: {last_session['status']}")
        logger.info(f"玩家数量: {stats.get('player_count', 0)}")
        logger.info(f"评论数量: {stats.get('comment_count', 0)}")
        logger.info(f"游戏次数: {stats.get('game_count', 0)}")

    prize_stats = stats.get('prize_stats', {})
    if prize_stats and logger:
        logger.info("奖品统计:")
        for prize, count in prize_stats.items():
            logger.info(f"  - {prize}: {count}个")

    if recent_comments and logger:
        logger.info("\n最近的评论:")
        for i, comment in enumerate(recent_comments[:5]):  # 只显示前5条
            logger.info(f"{i+1}. {comment['name']}: {comment['content']}")

    if queue_items and logger:
        logger.info("\n当前排队列表:")
        for i, item in enumerate(queue_items[:5]):  # 只显示前5个
            logger.info(f"{i+1}. {item['name']}({item['player_id'][:8]}...): 优先级 {item['priority']:.2f}")

    if logger:
        logger.info("\n" + "="*50)

    while True:
        choice = input("\n是否继续上次会话? (y/n): ").strip().lower()
        if choice in ('y', 'yes'):
            if logger:
                logger.info("将继续上次会话...")
            if last_session['status'] == 'closed':
                Play_db.reactivate_session(session_id)
                if logger:
                    logger.info(f"会话(ID:{session_id})已从关闭状态重新激活")
            Play_db.set_current_session_id(session_id)
            return session_id
        elif choice in ('n', 'no'):
            if logger:
                logger.info("将创建新会话...")
            if last_session['status'] == 'active':
                Play_db.close_session(session_id)
            return Play_db.create_new_session()
        else:
            if logger:
                logger.info("无效输入，请输入 y 或 n")

def load_session_data(session_id: int) -> Tuple[Dict, Dict, Dict, List]:
    """从数据库加载会话数据到内存"""
    if logger:
        logger.info(f"正在从数据库加载会话 {session_id} 的数据...")

    player_info = Play_db.get_player_info(session_id)
    player_comments = Play_db.get_player_comments(session_id)
    player_games = Play_db.get_player_games(session_id)
    queue = Play_db.reconstruct_queue(session_id)

    # 确保必要字段有默认值（已移除 paid 字段相关处理）
    if logger:
        logger.info(f"已加载 {len(player_info)} 名玩家信息, {sum(len(comments) for comments in player_comments.values())} 条评论, "
                    f"{sum(len(games) for games in player_games.values())} 条游戏记录, {len(queue)} 个队列项")

    return player_info, player_comments, player_games, queue

def init_session(config: Dict[str, Any], player_info: Dict, player_comments: Dict, player_games: Dict,
                in_que: List, current_player: Dict, player_info_lock: RLock,
                player_comments_lock: RLock, player_games_lock: RLock,
                queue_lock: Lock) -> Tuple[int, Any, Any, bool, Optional[str]]:
    """初始化会话，包括创建或加载会话数据、初始化OBS控制器和数据库同步管理器"""
    from play_processing import orderbook_status, orderbook_status_lock
    
    # 设置数据库配置
    Play_db.set_config(config)
    
    # 初始化 OBS 控制器
    obs_controller = Play_obs.init_obs_controller(config)

    # 会话管理: 检查或创建会话
    session_id = check_previous_session(config)
    if session_id is None:
        if logger:
            logger.critical("未能获取有效的会话ID。程序无法继续。")
        raise RuntimeError("未能获取有效的会话ID。")

    if logger:
        logger.info(f"当前使用会话 ID: {session_id}")
    
    # 修正：确保设置当前会话ID
    Play_db.set_current_session_id(session_id)
    
    session_info = Play_db.get_session_info_batch(session_id)
    session_details = session_info['session_details']
    is_new_session = session_info['is_new_session']
    
    session_start_time_iso = session_details.get('start_time') if session_details else None
    if logger:
        logger.info(f"会话开始时间: {session_start_time_iso}")

    # 初始化共享数据结构
    loaded_player_info_for_sync = None
    loaded_player_comments_for_sync = None
    loaded_player_games_for_sync = None
    
    if is_new_session:
        if logger:
            logger.info("使用新创建的会话，初始化空数据结构")
        with player_info_lock, player_comments_lock, player_games_lock, queue_lock:
            player_info.clear()
            player_comments.clear()
            player_games.clear()
            in_que.clear()
    else:
        if logger:
            logger.info(f"正在从数据库加载会话 {session_id} 的数据...")
        loaded_player_info, loaded_player_comments, loaded_player_games, loaded_queue = load_session_data(session_id)
        
        # 更新全局变量
        with player_info_lock:
            player_info.clear()
            player_info.update(loaded_player_info)
            
            # 添加缺失字段，包含 free_games_used_this_session
            for player_id in player_info:
                player_data = player_info[player_id]
                for field in ['temp_order_toVerify', 'temp_his_orderID_toUse', 'pending_game_entry_details', 'free_games_used_this_session']:
                    if field not in player_data:
                        # free_games_used_this_session 默认为 0，其他为 None
                        player_data[field] = 0 if field == 'free_games_used_this_session' else None
                
        with player_comments_lock:
            player_comments.clear()
            player_comments.update(loaded_player_comments)
        with player_games_lock:
            player_games.clear()
            player_games.update(loaded_player_games)
        with queue_lock:
            in_que.clear()
            in_que.extend(loaded_queue)

        # 重新计算有效评论数
        with player_comments_lock, player_games_lock, player_info_lock:
            for pid in list(player_info.keys()):
                _recalculate_and_update_comments_after_game(
                    pid, player_info, player_comments, player_games,
                    logger, cast(Optional[Lock], player_info_lock), 
                    cast(Optional[Lock], player_comments_lock), 
                    cast(Optional[Lock], player_games_lock)
                )

        # 重新排序队列
        if in_que:
            with queue_lock, player_info_lock, player_comments_lock, player_games_lock:
                play_processing.update_queue_priorities_inplace(in_que, player_info, player_comments, player_games, config)

        # 保存加载的数据
        loaded_player_info_for_sync = loaded_player_info
        loaded_player_comments_for_sync = loaded_player_comments
        loaded_player_games_for_sync = loaded_player_games
    
    # 新增：拉取所有pending_orders，初始化orderbook_status（保持原有状态）
    all_pending_orders = Play_db.get_all_pending_orders_with_status()
    with orderbook_status_lock:
        for order_row in all_pending_orders:
            order_id = order_row.get('order_id')
            order_status = order_row.get('status', 'available')
            if order_id:
                orderbook_status[order_id] = order_status
                if logger:
                    logger.debug(f"[会话初始化] 订单 {order_id} 状态初始化为: {order_status}")
    
    if all_pending_orders and logger:
        logger.info(f"[会话初始化] 已加载 {len(all_pending_orders)} 个订单到状态簿")

    # 初始化数据库同步管理器
    db_sync_manager = play_db_sync.init_sync_manager(config)
    
    if not is_new_session:
        db_sync_manager.start(session_id, queue_lock, current_player,
                            player_info_lock=cast(Optional[Lock], player_info_lock), 
                            player_comments_lock=cast(Optional[Lock], player_comments_lock),
                            player_games_lock=cast(Optional[Lock], player_games_lock), 
                            initial_player_info=loaded_player_info_for_sync,
                            initial_player_comments=loaded_player_comments_for_sync,
                            initial_player_games=loaded_player_games_for_sync)
    else:
        db_sync_manager.start(session_id, queue_lock, current_player,
                            player_info_lock=cast(Optional[Lock], player_info_lock), 
                            player_comments_lock=cast(Optional[Lock], player_comments_lock),
                            player_games_lock=cast(Optional[Lock], player_games_lock))
    
    return session_id, obs_controller, db_sync_manager, is_new_session, session_start_time_iso

def monitor_main_loop_worker(stop_flag: Dict[str, bool], last_loop_time_ref: List[float]):
    """监控主循环是否卡死的线程"""
    try:
        import logging
        logger = logging.getLogger(__name__)
    except Exception:
        logger = None

    timeout = 30  # 30秒超时
    if logger:
        logger.info(f"主循环监控已启动，超时设置为 {timeout} 秒")
    
    while True:
        try:
            if not stop_flag.get('running', True):
                if logger:
                    logger.info("主循环监控：收到停止信号，线程退出。")
                break
        except (EOFError, BrokenPipeError):
            if logger:
                logger.info("主循环监控：共享状态的连接已关闭，线程正常退出。")
            break  # 进程管理器已关闭，正常退出
        except Exception as e:
            if logger:
                logger.warning(f"主循环监控：检查状态时出错: {e}")
            break  # 发生其他错误时也退出

        try:
            time_since_last_loop = time.time() - last_loop_time_ref[0]
            
            if time_since_last_loop > timeout:
                if logger:
                    logger.error(f"主循环已 {time_since_last_loop:.1f} 秒未更新，可能已卡死")
                # 考虑是否需要额外的处理，比如强制退出程序
        except Exception as e:
            if logger:
                logger.error(f"主循环监控：计算循环时间时出错: {e}")
            break

        time.sleep(1)  # 每秒检查一次

    if logger:
        logger.info("主循环监控线程已退出")

async def _actual_order_scraping_logic(scraper, config, earliest_creation_time_str=None, current_logger: Optional[logging.Logger] = None):
    """实际执行订单抓取的异步函数"""
    orders = []
    try:
        if current_logger:
            current_logger.info(f"[订单线程] 开始抓取订单，最早时间限制: {earliest_creation_time_str or '无'}")
        orders = await scraper.scrape_orders(earliest_creation_time_str)
        if current_logger:
            current_logger.info(f"[订单线程] 抓取完成，获取到 {len(orders)} 个订单")
    except Exception as e:
        if current_logger:
            current_logger.error(f"[订单线程] 订单抓取失败: {e}")
            import traceback
            current_logger.error(traceback.format_exc())
    return orders

def order_fetching_worker(config, session_id, init_start_time_iso, player_info, player_info_lock, 
                         in_que, queue_lock, player_comments, player_games, 
                         player_comments_lock, player_games_lock, status_update_event, stop_flag,
                         last_successful_time_iso=None,
                         thread_last_active=None, thread_lock=None):  # 添加这两个参数
    """订单获取线程：定期从淘宝后台抓取订单并更新orderbook_status"""
    logger = logging.getLogger()
    import play_processing
    from play_getorder import OrderScraper
    import asyncio
    import time
    import re
    import os
    
    # 获取订单模块配置
    orders_config = config.get('orders', {})
    fetch_interval = orders_config.get('fetch_interval_seconds', 180)  # 修正：使用正确的配置键
    
    # 解析初始启动时间，用作最早的订单抓取时间点
    initial_fetch_time = None
    if init_start_time_iso:
        try:
            # 直接使用会话开始时间，不再进行偏移计算
            initial_fetch_time = datetime.strptime(init_start_time_iso, "%Y-%m-%d %H:%M:%S")
        except (ValueError, TypeError) as e:
            logger.error(f"[订单线程] 解析初始启动时间时出错: {e}")
            initial_fetch_time = None

    # 如果有传入上次成功时间，优先使用它而不是初始启动时间
    if last_successful_time_iso:
        try:
            initial_fetch_time = datetime.strptime(last_successful_time_iso, "%Y-%m-%d %H:%M:%S")
            logger.info(f"[订单线程] 使用上次成功抓取时间 {last_successful_time_iso} 作为起点")
        except (ValueError, TypeError) as e:
            logger.error(f"[订单线程] 解析上次成功时间时出错: {e}")
            # 仍保留之前设置的 initial_fetch_time

    # 确保格式正确的时间字符串，用于首次订单抓取
    earliest_order_time = initial_fetch_time.strftime("%Y-%m-%d %H:%M:%S") if initial_fetch_time else None
    logger.info(f"[订单线程] 订单获取线程已启动，最早抓取时间设为: {earliest_order_time or '不限制'}")

    # 保存最新的成功抓取时间，避免重复抓取
    last_successful_fetch_time = initial_fetch_time
    
    # 创建一次抓取器实例并重用
    scraper = None
    browser_initialized = False
    
    try:
        while True:
            try:
                # 检查停止标志
                if not stop_flag.get('running', True):
                    logger.info("[订单线程] 收到停止信号，线程退出。")
                    break
            except (EOFError, BrokenPipeError):
                logger.info("[订单线程] 共享状态连接已关闭，线程退出。")
                break
            
            # 更新心跳时间戳 - 使用传入的参数
            if thread_last_active is not None and thread_lock is not None:
                with thread_lock:
                    thread_last_active[0] = time.time()
            else:
                # 不再尝试使用全局变量，只记录警告
                logger.warning("[订单线程] 无法更新心跳状态，心跳参数未提供")
            
            try:
                # 订单抓取逻辑 - 使用异步任务包装以支持超时控制
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                if not scraper:
                    scraper = OrderScraper(config_path="config_getorder.yaml", logger=logger)
                    logger.info("[订单线程] 创建抓取器实例")
                
                # 初始化浏览器，如果需要
                if not browser_initialized:
                    logger.info("[订单线程] 初始化浏览器...")
                    init_success = loop.run_until_complete(scraper.initialize_browser())
                    if not init_success:
                        logger.error("[订单线程] 浏览器初始化失败，等待下次重试")
                        browser_initialized = False
                        time.sleep(fetch_interval)
                        continue
                    browser_initialized = True
                    logger.info("[订单线程] 浏览器初始化成功")
                
                # 设置超时时间，比抓取间隔稍长
                timeout_seconds = min(fetch_interval * 1.2, fetch_interval + 60)  # 至少多等60秒
                
                # 使用超时控制执行抓取任务
                try:
                    logger.info(f"[订单线程] 开始抓取订单，最早时间: {earliest_order_time or '不限制'}")
                    
                    # 使用超时控制调用异步函数
                    orders = asyncio.wait_for(
                        scraper.scrape_orders(earliest_creation_time_str=earliest_order_time),
                        timeout=timeout_seconds
                    )
                    orders = loop.run_until_complete(orders)
                    
                    # 处理抓取到的订单
                    now_time = datetime.now()
                    if orders:
                        logger.info(f"[订单线程] 成功抓取到 {len(orders)} 个订单")
                        
                        # 提取所有订单的时间，找到最新的一个
                        newest_time = None
                        for order in orders:
                            try:
                                creation_time = datetime.strptime(order['creation_time'], "%Y-%m-%d %H:%M:%S")
                                if newest_time is None or creation_time > newest_time:
                                    newest_time = creation_time
                            except (ValueError, KeyError) as e:
                                logger.warning(f"[订单线程] 解析订单时间失败: {e}, order={order}")
                        
                        if newest_time:
                            # 更新最后成功抓取时间
                            last_successful_fetch_time = newest_time
                            newest_time_str = newest_time.strftime("%Y-%m-%d %H:%M:%S")
                            logger.info(f"[订单线程] 更新最后成功抓取时间为: {newest_time_str}")
                            
                            # 保存状态
                            try:
                                from play_main import save_order_thread_state
                                save_order_thread_state(newest_time_str)
                            except ImportError:
                                logger.warning("[订单线程] 无法导入save_order_thread_state函数，状态未保存")
                        
                        # --- 关键修正：应用时间缓冲 ---
                        # 下次从最新时间减去缓冲时间开始抓取，以防漏单
                        buffer_seconds = orders_config.get('order_time_buffer_seconds', 5)
                        
                        # 添加空值检查
                        if last_successful_fetch_time:
                            next_fetch_time = last_successful_fetch_time - timedelta(seconds=buffer_seconds)
                            earliest_order_time = next_fetch_time.strftime("%Y-%m-%d %H:%M:%S")
                            logger.info(f"[订单线程] 下次抓取将从 {earliest_order_time} 开始 (已应用{buffer_seconds}秒缓冲)")
                        else:
                            logger.warning("[订单线程] 无法确定最后成功的抓取时间，下次将不设时间限制")
                            earliest_order_time = None
                        # --- 修正结束 ---
                        
                        # 处理每个订单
                        for order in orders:
                            process_single_order(order, play_processing.orderbook_status, player_info, player_info_lock)
                        
                        # 发送订单状态更新事件
                        if status_update_event:
                            status_summary = play_processing.get_orderbook_status_summary()
                            status_update_event.put({
                                'type': 'orderbook_status_update',
                                'summary': status_summary,
                                'timestamp': time.time()
                            })
                    else:
                        logger.info("[订单线程] 本次抓取未发现新订单")
                    
                except asyncio.TimeoutError:
                    logger.error(f"[订单线程] 抓取订单超时，超过 {timeout_seconds} 秒")
                    # 尝试重置浏览器
                    browser_initialized = False
                    if scraper:
                        try:
                            loop.run_until_complete(scraper.close())
                        except Exception as e_close:
                            logger.error(f"[订单线程] 关闭浏览器时出错: {e_close}")
                        scraper = None
                        
                except Exception as e_scrape:
                    logger.error(f"[订单线程] 抓取订单过程出错: {e_scrape}")
                    logger.error(traceback.format_exc())
                    # 尝试重置浏览器
                    browser_initialized = False
                    if scraper:
                        try:
                            loop.run_until_complete(scraper.close())
                        except Exception as e_close:
                            logger.error(f"[订单线程] 关闭浏览器时出错: {e_close}")
                        scraper = None
            
            except Exception as e:
                logger.error(f"[订单线程] 主循环异常: {e}")
                logger.error(traceback.format_exc())
            
            finally:
                # 再次更新心跳时间戳
                if thread_last_active is not None and thread_lock is not None:
                    with thread_lock:
                        thread_last_active[0] = time.time()
                else:
                    # 不再尝试使用全局变量，只记录警告
                    logger.warning("[订单线程] 无法更新心跳状态(finally块)，心跳参数未提供")
                    
                # 关闭事件循环
                if 'loop' in locals() and loop:
                    loop.close()
            
            # 休眠到下一次获取
            logger.info(f"[订单线程] 休眠 {fetch_interval} 秒后进行下次抓取")
            time.sleep(fetch_interval)
    
    finally:
        # 确保关闭浏览器资源
        if scraper:
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(scraper.close())
                loop.close()
            except Exception as e_close:
                logger.error(f"[订单线程] 最终关闭浏览器时出错: {e_close}")
        
        logger.info("[订单线程] 订单获取线程已停止")

def process_single_order(order_data, orderbook_status, player_info, player_info_lock):
    """处理单个订单的函数，与主线程隔离错误"""
    logger = logging.getLogger()
    try:
        order_id = order_data.get('order_id')
        buyer_account = order_data.get('buyer_account')
        creation_time = order_data.get('creation_time')
        
        if not order_id or not buyer_account:
            logger.warning(f"[订单处理] 订单数据不完整: {order_data}")
            return False
        
        # 记录订单
        with play_processing.orderbook_status_lock:
            # 如果订单不在状态簿中，添加为可用状态
            if order_id not in play_processing.orderbook_status:
                play_processing.orderbook_status[order_id] = 'available'
                logger.info(f"[订单处理] 新增订单 {order_id} 到状态簿，买家: {buyer_account}, 创建时间: {creation_time}")
        
        # 处理玩家的待验证订单
        with player_info_lock:
            # 查找匹配的待验证订单号
            for player_id, player_data in player_info.items():
                temp_order = player_data.get('temp_order_toVerify')
                if temp_order and temp_order == order_id:
                    logger.info(f"[订单处理] 为玩家 {player_id} 验证订单 {order_id}")
                    
                    # 清空待验证订单号并设置已验证订单
                    player_info[player_id]['temp_order_toVerify'] = None
                    player_info[player_id]['temp_his_orderID_toUse'] = order_id
                    
                    # 同步到数据库
                    import play_db_sync
                    if play_db_sync.db_sync_manager:
                        play_db_sync.db_sync_manager.add_sync_task("update_player_order_state", {
                            'player_id': player_id,
                            'temp_order_toVerify': None,
                            'temp_his_orderID_toUse': order_id
                        })
                    
                    logger.info(f"[订单处理] 玩家 {player_id} 的订单 {order_id} 已验证并可用")
        
        return True
    except Exception as e:
        logger.error(f"[订单处理] 处理订单 {order_data.get('order_id', 'unknown')} 时出错: {e}")
        logger.error(traceback.format_exc())
        return False
