import socket
import json
import time
import threading
import logging
from typing import Dict, List, Optional, Any, MutableMapping, Callable
import queue

class DetectionReceiver:
    """物体检测数据接收器"""
    
    def __init__(self, host: str, port: int, stop_flag: MutableMapping[str, bool], 
                object_update_callback: Optional[Callable] = None,
                status_update_queue: Optional[queue.Queue] = None):
        self.host = host
        self.port = port
        self.stop_flag = stop_flag
        self.logger = logging.getLogger(__name__)
        self.object_update_callback = object_update_callback
        self.status_update_queue = status_update_queue

        # 数据存储
        self.latest_detection_data = {}
        self.data_lock = threading.Lock()
        
        # 连接状态
        self.socket = None
        self._socket_file = None  # 新增：socket文件对象
        self.is_connected = False
        self.last_connection_state = None # 新增：记录上次连接状态
        self.last_receive_time = 0
        
        # 统计信息
        self.stats = {
            'total_received': 0,
            'parse_errors': 0,
            'connection_errors': 0,
            'last_fps': 0,
            'last_process_time': 0
        }
        
    def connect(self) -> bool:
        """建立TCP连接并订阅检测推送"""
        try:
            if self.socket:
                self.socket.close()
            if self._socket_file:
                self._socket_file.close()
                
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(5.0)  # 连接超时
            self.socket.connect((self.host, self.port))
            
            # 设置接收超时并创建文件对象
            self.socket.settimeout(1.0)
            self._socket_file = self.socket.makefile('r', encoding='utf-8')
            self.is_connected = True
            self.logger.info(f"成功连接到检测服务器 {self.host}:{self.port}")
            
            # --- 新增：当状态从"断开"变为"连接"时，发送重连成功事件 ---
            if self.last_connection_state != 'connected' and self.status_update_queue:
                self.status_update_queue.put({
                    'type': 'detect_service_event',
                    'event_name': 'detect_service_reconnected'
                })
            self.last_connection_state = 'connected'
            # --- 新增结束 ---
            
            # 发送订阅请求
            try:
                subscribe_command = json.dumps({"command": "subscribe"}) + "\n"
                self.socket.sendall(subscribe_command.encode("utf-8"))
                self.logger.debug("已发送订阅命令")
            except Exception as e:
                self.logger.error(f"发送订阅命令失败: {e}")
                self.is_connected = False
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"连接检测服务器失败: {e}")
            self.is_connected = False
            self.stats['connection_errors'] += 1
            
            # --- 新增：当状态从"连接"变为"断开"时，发送断开事件 ---
            if self.last_connection_state != 'disconnected' and self.status_update_queue:
                self.status_update_queue.put({
                    'type': 'detect_service_event',
                    'event_name': 'detect_service_disconnected'
                })
            self.last_connection_state = 'disconnected'
            # --- 新增结束 ---
            
            return False
    
    def disconnect(self):
        """断开连接"""
        try:
            if self._socket_file:
                self._socket_file.close()
                self._socket_file = None
            if self.socket:
                self.socket.close()
                self.socket = None
            self.is_connected = False
            self.logger.info("已断开检测服务器连接")

            # --- 新增：当状态从"连接"变为"断开"时，发送断开事件 ---
            if self.last_connection_state != 'disconnected' and self.status_update_queue:
                self.status_update_queue.put({
                    'type': 'detect_service_event',
                    'event_name': 'detect_service_disconnected'
                })
            self.last_connection_state = 'disconnected'
            # --- 新增结束 ---

        except Exception as e:
            self.logger.error(f"断开连接时出错: {e}")
    
    def receive_data(self) -> Optional[Dict[str, Any]]:
        """接收并解析一条检测数据"""
        if not self.is_connected or not self._socket_file:
            return None
            
        try:
            # 读取完整的一行数据
            line = self._socket_file.readline()
            if not line:
                self.logger.warning("服务器关闭了连接")
                self.disconnect()  # 立即清理连接
                return None
                
            json_line = line.strip()
            if not json_line:
                return None
                
            # 解析JSON
            try:
                data = json.loads(json_line)
                self.last_receive_time = time.time()
                self.stats['total_received'] += 1
                
                # 修复数据类型转换
                self.stats['received_bytes'] = self.stats.get('received_bytes', 0) + len(json_line)
                self.stats['fps'] = int(time.time() - self.last_receive_time)
                
                return data
            except json.JSONDecodeError as e:
                self.logger.error(f"JSON解析错误: {e}, 数据: {json_line[:100]}...")
                self.stats['parse_errors'] += 1
                return None
                
        except socket.timeout:
            # 超时不是错误，继续尝试
            return None
        except Exception as e:
            self.logger.error(f"接收数据时发生未知错误: {e}")
            self.disconnect()  # 立即清理连接
            return None

    def process_detection_data(self, data: Dict[str, Any]):
        """处理检测数据"""
        try:
            current_status = data.get('status')

            if current_status == 'subscribed':
                if 'data' not in data:
                    self.logger.info(f"收到订阅确认: {data.get('message', '无详细信息')}")
                    return
                
            if current_status != 'data_updated':
                self.logger.warning(f"收到非预期或非数据更新状态的数据: {current_status}")
                return
                
            detection_data = data.get('data', {})
            if not detection_data:
                self.logger.debug("收到 data_updated 状态但 data 字段为空")
                return
                
            # 修复：确保使用float类型而非整数类型
            self.stats['last_fps'] = float(detection_data.get('fps', 0))
            self.stats['last_process_time'] = float(detection_data.get('process_time_ms', 0))
            
            objects = detection_data.get('objects', [])
            
            new_object_map = {}
            for obj in objects:
                track_number = obj.get('track_number')
                class_name = obj.get('class_name')
                if track_number is not None and class_name:
                    new_object_map[str(track_number)] = class_name
            
            with self.data_lock:
                self.latest_detection_data = {
                    'timestamp': detection_data.get('timestamp', time.time()),
                    'fps': detection_data.get('fps', 0),
                    'process_time_ms': detection_data.get('process_time_ms', 0),
                    'objects_count': len(objects),
                    'objects': objects,  # 完整的物体列表包含归一化坐标
                    'object_map': new_object_map,
                    'receive_time': time.time()
                }
            
            if self.object_update_callback and self.status_update_queue:
                try:
                    self.object_update_callback(new_object_map, objects, self.status_update_queue)
                except Exception as e:
                    self.logger.error(f"物体映射与数据更新回调执行失败: {e}")

            # 日志记录（降低频率）
            # if self.stats['total_received'] % 100 == 0:
            #     self.logger.debug(f"检测数据统计: 收到{self.stats['total_received']}条, "
            #                     f"当前FPS: {self.stats['last_fps']:.1f}, "
            #                     f"物体数: {len(objects)}, 映射数: {len(new_object_map)}")
                
        except Exception as e:
            self.logger.error(f"处理检测数据时出错: {e}")
    
    def get_latest_data(self) -> Optional[Dict[str, Any]]:
        """获取最新的检测数据"""
        with self.data_lock:
            if not self.latest_detection_data:
                return None
            return self.latest_detection_data.copy()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self.data_lock:
            stats_copy = self.stats.copy()
            stats_copy['is_connected'] = self.is_connected
            stats_copy['last_receive_time'] = self.last_receive_time
            stats_copy['data_age_seconds'] = time.time() - self.latest_detection_data.get('receive_time', 0) if self.latest_detection_data else float('inf')
        return stats_copy
    
    def run(self):
        """主运行循环"""
        self.logger.info("检测数据接收器启动")
        reconnect_delay = 1.0
        max_reconnect_delay = 30.0
        
        while self.stop_flag.get('running', True):
            try:
                # 尝试连接
                if not self.is_connected:
                    if self.connect():
                        reconnect_delay = 1.0  # 重置重连延迟
                    else:
                        self.logger.info(f"连接失败，{reconnect_delay:.1f}秒后重试")
                        time.sleep(min(reconnect_delay, max_reconnect_delay))
                        reconnect_delay = min(reconnect_delay * 1.5, max_reconnect_delay)
                        continue
                
                # 接收和处理数据
                data = self.receive_data()
                if data:
                    # self.logger.debug(f"原始物体检测信息接到的是: {data}")   # 显示接到的数据
                    self.process_detection_data(data)
                elif not self.is_connected:
                    # 连接断开，准备重连
                    self.logger.warning("检测到连接断开，准备重连")
                    continue
                    
            except Exception as e:
                self.logger.error(f"检测接收器运行时出错: {e}")
                self.is_connected = False
                time.sleep(1.0)
        
        # 清理
        self.disconnect()
        self.logger.info("检测数据接收器已停止")

# 全局接收器实例
detection_receiver = None

def start_detection_receiver(config: Dict[str, Any], stop_flag: MutableMapping[str, bool], 
                           object_update_callback: Optional[Callable] = None,
                           status_update_queue: Optional[queue.Queue] = None) -> DetectionReceiver:
    """启动检测数据接收器"""
    global detection_receiver
    
    detect_config = config.get('Detect_server', {})
    host = detect_config.get('host', 'localhost')
    port = detect_config.get('port', 5555)
    
    detection_receiver = DetectionReceiver(host, port, stop_flag, object_update_callback, status_update_queue)
    
    # 启动接收线程
    thread = threading.Thread(
        target=detection_receiver.run,
        daemon=True,
        name="DetectionReceiver"
    )
    thread.start()
    
    return detection_receiver

def get_detection_receiver() -> Optional[DetectionReceiver]:
    """获取检测接收器实例"""
    return detection_receiver
