# 可被调用，也可独立执行。建议正式工作前，先独立运行抓取一次订单，看网页有无登录、弹出窗口问题。
# 独立运行时日志输出到终端；被调用时日志输出到主程序日志。
# 被调用抓多次时重复使用第一次的订单筛选条件。暂未开发过程中变更筛选条件的功能。
import asyncio
import random
import os
import sys
import yaml
from playwright.async_api import async_playwright, Playwright # Playwright hinzugefügt
import playwright.async_api  # 不加这句编译后会找不到模块
import traceback
from datetime import datetime
import functools  # 导入functools模块，用于超时装饰器

# --- 新增：添加超时装饰器 ---
def with_timeout(timeout_seconds=60):
    """超时装饰器，为异步函数添加超时控制"""
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                # 创建任务
                task = asyncio.create_task(func(*args, **kwargs))
                # 等待任务完成，设置超时
                result = await asyncio.wait_for(task, timeout=timeout_seconds)
                return result
            except asyncio.TimeoutError:
                logger = kwargs.get('logger')
                if logger:
                    logger.error(f"函数 {func.__name__} 执行超时 ({timeout_seconds}秒)")
                else:
                    print(f"[错误] 函数 {func.__name__} 执行超时 ({timeout_seconds}秒)")
                raise TimeoutError(f"函数 {func.__name__} 执行超时")
        return wrapper
    return decorator
# --- 新增结束 ---

def get_local_path(filename):
    """获取本地文件路径，支持打包后的exe和脚本运行"""
    # 检测是否为编译后的程序
    is_compiled = False

    # 多种检测方法
    if (hasattr(sys, '_MEIPASS') or  # PyInstaller
        getattr(sys, 'frozen', False) or  # 标准frozen检测
        sys.argv[0].endswith('.exe') or  # exe文件
        os.path.abspath(sys.executable) == os.path.abspath(sys.argv[0])):  # executable等于argv[0]
        is_compiled = True

    # 额外检查：如果__file__在临时目录，也认为是编译后的
    if not is_compiled and '__file__' in globals():
        file_path = os.path.abspath(__file__)
        if 'temp' in file_path.lower() or 'onefil' in file_path.lower():
            is_compiled = True

    if is_compiled:
        # 使用sys.executable的目录
        base_path = os.path.dirname(os.path.abspath(sys.executable))
    else:
        # 脚本模式，使用__file__
        base_path = os.path.dirname(os.path.abspath(__file__))

    return os.path.join(base_path, filename)

# 加载配置文件 (模块级函数)
def load_config(config_path="config_getorder.yaml"):
    """加载YAML配置文件"""
    try:
        # 使用get_local_path获取配置文件路径
        config_path = get_local_path(config_path)
        if not os.path.exists(config_path):
            print(f"[警告] 配置文件 '{config_path}' 未找到。将使用默认配置。")
            # 返回包含所有必要键的默认配置
            return {
                    "browser": {"use_user_data": False, "user_data_dir": "./user_data_default", "headless": False},
                    "order_query": {"order_status": "买家已付款", "product_id": "", "max_pages": 5},
                    "delays": {
                        "navigation": {"min": 1.0, "max": 2.0}, "click": {"min": 0.5, "max": 1.5},
                        "input": {"min": 0.2, "max": 0.6}, "hover": {"min": 0.1, "max": 0.3},
                        "search_wait": {"min": 1.5, "max": 3.0}, "process_order": {"min": 0.1, "max": 0.4},
                        "after_login": {"min": 0.5, "max": 1.5}, "after_popup": {"min": 0.5, "max": 1.0},
                        "slow_mo": 50,
                        "after_manual_captcha_input": {"min": 2.8, "max": 3.2},
                        "after_body_click_popup": {"min": 0.3, "max": 0.6},
                        "login_check_initial_wait": {"min": 1.0, "max": 2.0},
                        "post_initial_captcha_wait": {"min": 2.0, "max": 3.0},
                        "after_order_status_selector_wait": {"min": 0.5, "max": 1.0},
                        "after_dropdown_trigger_click": {"min": 0.3, "max": 0.8},
                        "after_js_dropdown_fallback": {"min": 0.8, "max": 1.2},
                        "after_pid_clear": {"min": 0.1, "max": 0.3},
                        "after_search_captcha_handled": {"min": 0.5, "max": 1.0},
                        "between_order_item_processing": {"min": 0.05, "max": 0.2},
                        "standalone_end_wait": {"min": 2.8, "max": 3.2}
                    }
                }
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
            print(f"[信息] 成功加载配置文件: '{config_path}'")
            # 确保所有必要的延迟键存在，补充默认值
            default_delays = {
                "navigation": {"min": 1.0, "max": 2.0}, "click": {"min": 0.5, "max": 1.5},
                "input": {"min": 0.2, "max": 0.6}, "hover": {"min": 0.1, "max": 0.3},
                "search_wait": {"min": 1.5, "max": 3.0}, "process_order": {"min": 0.1, "max": 0.4}, # Main loop delay
                "after_login": {"min": 0.5, "max": 1.5}, "after_popup": {"min": 0.5, "max": 1.0},
                "slow_mo": 50,
                # New defaults
                "after_manual_captcha_input": {"min": 2.8, "max": 3.2},
                "after_body_click_popup": {"min": 0.3, "max": 0.6},
                "login_check_initial_wait": {"min": 1.0, "max": 2.0},
                "post_initial_captcha_wait": {"min": 2.0, "max": 3.0},
                "after_order_status_selector_wait": {"min": 0.5, "max": 1.0},
                "after_dropdown_trigger_click": {"min": 0.3, "max": 0.8},
                "after_js_dropdown_fallback": {"min": 0.8, "max": 1.2},
                "after_pid_clear": {"min": 0.1, "max": 0.3},
                "after_search_captcha_handled": {"min": 0.5, "max": 1.0},
                "between_order_item_processing": {"min": 0.05, "max": 0.2}, # Specific for per-item loop
                "standalone_end_wait": {"min": 2.8, "max": 3.2}
            }
            loaded_delays = config.get("delays", {})

            for key, default_value in default_delays.items():
                if key not in loaded_delays:
                    loaded_delays[key] = default_value
                elif isinstance(default_value, dict): # For min/max pairs
                     if not isinstance(loaded_delays.get(key), dict): 
                         loaded_delays[key] = default_value
                     else:
                         for sub_key, default_sub_value in default_value.items():
                             if sub_key not in loaded_delays[key]: 
                                 loaded_delays[key][sub_key] = default_sub_value
            
            config["delays"] = loaded_delays

            if "order_query" not in config: # Ensure order_query exists
                config["order_query"] = {"order_status": "买家已付款", "product_id": "", "max_pages": 5}
            if "browser" not in config: # Ensure browser exists
                config["browser"] = {"use_user_data": False, "user_data_dir": "./user_data_default", "headless": False}
            return config
    except Exception as e:
        print(f"[错误] 加载配置文件时出错: {e}")
        print("[信息] 将使用默认配置。")
        return { # Fallback default config
            "browser": {"use_user_data": False, "user_data_dir": "./user_data_default", "headless": False},
            "order_query": {"order_status": "买家已付款", "product_id": "", "max_pages": 5},
            "delays": {
                "navigation": {"min": 1.0, "max": 2.0}, "click": {"min": 0.5, "max": 1.5},
                "input": {"min": 0.2, "max": 0.6}, "hover": {"min": 0.1, "max": 0.3},
                "search_wait": {"min": 1.5, "max": 3.0}, "process_order": {"min": 0.1, "max": 0.4},
                "after_login": {"min": 0.5, "max": 1.5}, "after_popup": {"min": 0.5, "max": 1.0},
                "slow_mo": 50,
                "after_manual_captcha_input": {"min": 2.8, "max": 3.2},
                "after_body_click_popup": {"min": 0.3, "max": 0.6},
                "login_check_initial_wait": {"min": 1.0, "max": 2.0},
                "post_initial_captcha_wait": {"min": 2.0, "max": 3.0},
                "after_order_status_selector_wait": {"min": 0.5, "max": 1.0},
                "after_dropdown_trigger_click": {"min": 0.3, "max": 0.8},
                "after_js_dropdown_fallback": {"min": 0.8, "max": 1.2},
                "after_pid_clear": {"min": 0.1, "max": 0.3},
                "after_search_captcha_handled": {"min": 0.5, "max": 1.0},
                "between_order_item_processing": {"min": 0.05, "max": 0.2},
                "standalone_end_wait": {"min": 2.8, "max": 3.2}
            }
        }

class OrderScraper:
    def __init__(self, config_path="config_getorder.yaml", logger=None, interactive=False):
        self.config = load_config(config_path)
        self.logger = logger
        self.interactive = interactive
        self.p = None
        self.browser = None
        self.page = None
        self.delays = self.config.get("delays", {})
        self.browser_config = self.config.get("browser", {})
        self.order_query_config = self.config.get("order_query", {})
        self._search_conditions_set = False  # 新增：标记是否已设置过筛选条件
        
        # 新增：获取超时配置（如果是通过play_main.py中的config传入，会由调用方覆盖）
        self.browser_init_timeout = self.config.get('orders', {}).get('browser_init_timeout', 180)
        self.orders_scrape_timeout = self.config.get('orders', {}).get('orders_scrape_timeout', 300)

    def _log_or_print(self, msg, level="info"):
        if self.logger:
            if level == "info":
                self.logger.info(msg)
            elif level == "warning":
                self.logger.warning(msg)
            elif level == "error":
                self.logger.error(msg)
            elif level == "debug":
                self.logger.debug(msg)
            else:
                self.logger.info(msg)
        else:
            print(msg)

    async def _random_async_delay(self, category_or_min, max_val=None):
        if isinstance(category_or_min, str) and max_val is None:
            delay_setting = self.delays.get(category_or_min, {"min": 0.3, "max": 0.8})
            min_time = delay_setting.get("min", 0.3)
            max_time = delay_setting.get("max", 0.8)
        elif isinstance(category_or_min, (int, float)) and isinstance(max_val, (int, float)):
            min_time = category_or_min
            max_time = max_val
        else:
            min_time = 0.3; max_time = 0.8
        
        delay = random.uniform(min_time, max_time)
        self._log_or_print(f"[随机延迟] {delay:.2f}秒 (类别/范围: {category_or_min if isinstance(category_or_min, str) else str(min_time)+'-'+str(max_time)})...", "debug")
        await asyncio.sleep(delay)

    async def _check_for_slider_captcha(self):
        if not self.page: return False
        captcha_selectors = [".nc_wrapper", ".nc-lang-cnt", "div[class*='verify']", "div[class*='captcha']", "div[class*='slidecode']", "iframe[src*='verify']"]
        for selector in captcha_selectors:
            locator = self.page.locator(selector).first
            try:
                try:
                    await locator.wait_for(state="visible", timeout=1000)
                    if await locator.is_visible():
                        self._log_or_print(f"\n[!] 检测到可能的滑块验证码 ({selector})，请手动完成验证...", "warning")
                        await self.page.screenshot(path="captcha_detected.png")
                        self._log_or_print("[!] 已保存验证码截图到 captcha_detected.png")
                        if self.logger:
                            self._log_or_print("[需要人工操作] 检测到滑块验证码，等待10秒后自动继续。", "warning")
                            await asyncio.sleep(10)
                        else:
                            print("[!] 脚本已暂停，请手动完成验证，完成后按回车继续...")
                            input("[按回车继续...]")
                        self._log_or_print("[!] 验证操作完成，等待页面响应...")
                        await self._random_async_delay("after_manual_captcha_input")
                        try:
                            cookies = await self.page.context.cookies()
                            x5sec_cookie = next((c for c in cookies if 'name' in c and 'x5sec' in c['name'].lower()), None)
                            if x5sec_cookie:
                                self._log_or_print("[+] 检测到 x5sec相关 Cookie...")
                            else:
                                self._log_or_print("[-] 未在当前上下文中检测到 x5sec相关 Cookie。")
                        except Exception as e_cookie:
                            self._log_or_print(f"[!] 检查Cookie时出错: {e_cookie}", "warning")
                        return True
                except Exception:
                    pass
            except Exception: pass
        return False

    async def _close_popup_if_present(self):
        if not self.page: return False
        self._log_or_print("检查是否有弹窗广告或提示...")
        popup_close_selectors = [
            "div[class*='close']", "button[class*='close']", "i[class*='close']", "span[class*='close']", "a[class*='close']",
            "div[aria-label*='关闭']", "button[aria-label*='关闭']", "div:has-text('×')", "span:has-text('×')",
            "div:has-text('关闭')", "button:has-text('关闭')", ".J_AdsCloseIcon", "button[aria-label='关闭']",
            ".tb-tc-close-button", ".close-btn", ".J_ShopSignBannerClose", ".next-dialog-close", ".modal-close",
            "img[alt*='close']", "img[alt*='关闭']",
        ]
        closed_specific_popup = False

        for selector in popup_close_selectors:
            try:
                close_button = self.page.locator(selector).first
                try:
                    await close_button.wait_for(state="visible", timeout=500)
                    if await close_button.is_visible():
                        self._log_or_print(f"检测到弹窗/提示，尝试点击关闭按钮: {selector}")
                        try:
                            await close_button.click(timeout=1000)
                            self._log_or_print(f"已点击关闭按钮: {selector}")
                            closed_specific_popup = True
                            await self._random_async_delay("after_popup") 
                            break 
                        except Exception as e_click:
                            self._log_or_print(f"点击关闭按钮 {selector} 失败: {e_click}", "warning")
                except Exception:
                    pass
            except Exception: pass
        
        action_taken = closed_specific_popup
        if not closed_specific_popup:
            self._log_or_print("未通过特定按钮关闭弹窗/提示。尝试点击页面主体以消除可能的浮动通知...")
            try:
                await self.page.locator("body").click(timeout=1000, trial=True)
                self._log_or_print("已尝试点击页面主体。")
                action_taken = True
                await self._random_async_delay("after_body_click_popup") 
            except Exception as e_body_click:
                self._log_or_print(f"点击页面主体时出现问题 (可能无影响): {e_body_click}", "warning")

        if action_taken:
            self._log_or_print("执行了关闭弹窗/点击空白的操作。")
            if await self._check_for_slider_captcha():
                self._log_or_print("关闭弹窗/提示或点击空白后出现验证码，请手动处理。", "warning")
        else:
            self._log_or_print("未检测到或未能关闭明确的弹窗/提示。")
        return action_taken

    async def _check_login_status(self, target_url_keyword="liveplatform.taobao.com/restful/index/shop/sold-list"):
        if not self.page: return False
        self._log_or_print("正在检查登录状态...")
        await self._random_async_delay("login_check_initial_wait")
        current_url = self.page.url
        self._log_or_print(f"[登录检查] 当前URL: {current_url}")

        login_page_domains = ["login.taobao.com", "passport.taobao.com", "login.tmall.com"]
        for domain in login_page_domains:
            if domain in current_url:
                msg = f"[登录状态] 当前URL '{current_url}' 属于登录域名 '{domain}'."
                self._log_or_print(msg)
                login_form_selectors = ["input#fm-login-id", "div.login-content", "button[type='submit']#login-submit", "button.fm-button.fm-submit.password-login", "iframe[src*='login']"]
                for selector in login_form_selectors:
                    try:
                        locator = self.page.locator(selector)
                        try:
                            await locator.wait_for(state="visible", timeout=500)
                            if await locator.is_visible():
                                msg = f"[登录状态] 在登录页URL下检测到登录元素 '{selector}'，确认为未登录。"
                                self._log_or_print(msg)
                                return False
                        except Exception:
                            pass
                    except Exception: pass
                msg = f"[登录状态] URL为登录页，但未找到特定登录框元素，仍判断为未登录。"
                self._log_or_print(msg)
                return False

        embedded_login_selectors = [
            "iframe[id*='login']", "iframe[name*='login']", "iframe[src*='login.taobao.com']", "iframe[src*='passport.taobao.com']",
            "div#dialog-login", "div.login-panel", "input#fm-login-id", "button.login-button"
        ]
        for selector in embedded_login_selectors:
            try:
                locator = self.page.locator(selector).first
                try:
                    await locator.wait_for(state="visible", timeout=1000)
                    if await locator.is_visible():
                        msg = f"[登录状态] 在当前页面检测到嵌入式登录相关元素 '{selector}'，判断为未登录。"
                        self._log_or_print(msg)
                        return False
                except Exception:
                    pass
            except Exception: pass
        
        if target_url_keyword in current_url:
            order_management_nav_locator = self.page.locator("ul.third-level-wrap--pliRWmI2 li:has-text('订单管理')").first
            order_list_container_selector = "div.next-table-body"
            try:
                await order_management_nav_locator.wait_for(state="visible", timeout=3000)
                msg = "[登录状态] 检测到商家后台 '订单管理' 导航元素。"
                self._log_or_print(msg)
                container_locator = self.page.locator(order_list_container_selector)
                try:
                    await container_locator.wait_for(state="visible", timeout=3000)
                    if await container_locator.is_visible():
                        msg = "[登录状态] 同时检测到订单列表容器，判断为已登录且页面已准备好。"
                        self._log_or_print(msg)
                        return True
                    else:
                        msg = "[登录状态] '订单管理'导航可见，但订单列表容器未立即可见。可能页面仍在加载或有弹窗。倾向于认为已登录。"
                        self._log_or_print(msg)
                        return True 
                except Exception:
                    msg = "[登录状态] '订单管理'导航可见，但订单列表容器未立即可见。可能页面仍在加载或有弹窗。倾向于认为已登录。"
                    self._log_or_print(msg)
                    return True
            except Exception:
                msg = f"[登录状态] 当前URL '{current_url}' 包含目标关键词，但未在超时内找到 '订单管理' 导航元素。"
                self._log_or_print(msg)
                slider_locator = self.page.locator(".nc_wrapper")
                try:
                    await slider_locator.wait_for(state="visible", timeout=500)
                    if not await slider_locator.is_visible():
                        msg = "[登录状态] ...且未检测到滑块。可能页面加载不完整或结构变化。"
                        self._log_or_print(msg)
                except Exception:
                    msg = "[登录状态] ...且未检测到滑块。可能页面加载不完整或结构变化。"
                    self._log_or_print(msg)
                return False
        
        msg = f"[登录状态] 未能通过特定检查确认登录状态，或当前URL '{current_url}' 不符合预期。默认为未登录或需用户检查。"
        self._log_or_print(msg)
        return False

    # --- 修改：使用超时装饰器包装浏览器初始化函数 ---
    @with_timeout(timeout_seconds=180)  # 3分钟超时
    async def initialize_browser(self):
        # 覆盖装饰器的默认超时值 - 修复：正确引用实例方法
        self.__wrapped__ = self.initialize_browser.__wrapped__
        
        if self.browser:
            self._log_or_print("[信息] 浏览器已初始化。")
            return True

        try:
            self.p = await async_playwright().start()
            user_data_dir_config = self.browser_config.get("user_data_dir", "./user_data_default")
            use_user_data_config = self.browser_config.get("use_user_data", False)
            
            # 保证 actual_user_data_dir 始终为字符串路径
            if use_user_data_config:
                actual_user_data_dir = user_data_dir_config
                if not os.path.exists(actual_user_data_dir):
                    try:
                        os.makedirs(actual_user_data_dir, exist_ok=True)
                        self._log_or_print(f"[信息] 用户数据目录 '{actual_user_data_dir}' 不存在，已创建。")
                    except Exception as e:
                        self._log_or_print(f"[警告] 创建用户数据目录 '{actual_user_data_dir}' 失败: {e}。", "warning")
            else:
                actual_user_data_dir = "./user_data_default"
            
            launch_args = [
                '--window-size=1600,768', '--disable-extensions', '--hide-scrollbars',
                '--disable-bundled-ppapi-flash', '--mute-audio', '--no-sandbox',
                '--disable-setuid-sandbox', '--disable-infobars', '--force-device-scale-factor=1',
            ]
            try:
                self.browser = await self.p.chromium.launch_persistent_context(
                    actual_user_data_dir,
                    headless=self.browser_config.get("headless", False),
                    args=['--disable-blink-features=AutomationControlled'] + launch_args,
                    user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36",
                    slow_mo=self.delays.get("slow_mo", 50)
                )
            except Exception as e:
                self._log_or_print(f"[错误] 启动浏览器失败: {e}", "error")
                self._log_or_print("[提示] 如果错误信息提及 'user_data_dir is used by another browser', 请关闭所有使用该配置文件的Chrome实例，或更改配置文件中的 'user_data_dir'。", "error")
                if self.p: await self.p.stop(); self.p = None
                return False

            await self.browser.add_init_script("""
                Object.defineProperty(navigator, 'webdriver', { get: () => undefined });
                Object.defineProperty(navigator, 'languages', { get: () => ['zh-CN', 'zh'] });
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [
                        { name: 'Chrome PDF Plugin', filename: 'internal-pdf-viewer', description: 'Portable Document Format' },
                        { name: 'Chrome PDF Viewer', filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai', description: '' },
                        { name: 'Native Client', filename: 'internal-nacl-plugin', description: '' }
                    ]
                });
                if (!window.chrome) window.chrome = {};
                if (!window.chrome.runtime) window.chrome.runtime = {};
            """)
            
            self.page = await self.browser.new_page()
            await self.page.set_viewport_size({"width": 1680, "height": 1024})

            self._log_or_print("正在导航到淘宝商家后台订单列表页面...")
            try:
                await self.page.goto(
                    "https://liveplatform.taobao.com/restful/index/shop/sold-list#/tp/sold-list",
                    wait_until="domcontentloaded", timeout=30000
                )
                await self._random_async_delay("navigation")
            except Exception as e_goto:
                self._log_or_print(f"[错误] 导航到目标页面失败: {e_goto}", "error")
                await self.close(); return False

            if await self._check_for_slider_captcha():
                self._log_or_print("初始加载后，验证码已处理，将重新检查登录状态...")
                await self._random_async_delay("post_initial_captcha_wait")

            logged_in = await self._check_login_status()
            if not logged_in:
                self._log_or_print("[需要人工操作] 检测到未登录，等待10秒后自动重试。", "warning")
                await asyncio.sleep(10)
                self._log_or_print("用户已确认操作或已自动等待，继续执行脚本...")
                await self._random_async_delay("after_login")
            else:
                self._log_or_print("[信息] 系统检测到已登录或页面已就绪。")
                await self._random_async_delay("after_login")

            await self._close_popup_if_present()
            self._log_or_print("[信息] 浏览器初始化完成。")
            return True
        except Exception as e:
            self._log_or_print(f"[错误] 启动浏览器失败: {e}", "error")
            self._log_or_print("[提示] 如果错误信息提及 'user_data_dir is used by another browser', 请关闭所有使用该配置文件的Chrome实例，或更改配置文件中的 'user_data_dir'。", "error")
            if self.p:
                await self.p.stop()
                self.p = None
            return False

    # --- 修改：使用超时装饰器包装订单抓取函数 ---
    @with_timeout(timeout_seconds=300)  # 5分钟超时
    async def scrape_orders(self, earliest_creation_time_str=None, order_status_override=None,
                            product_id_override=None, max_pages_override=None):
        # 覆盖装饰器的默认超时值 - 修复：正确引用实例方法
        self.__wrapped__ = self.scrape_orders.__wrapped__
        
        if not self.page or not self.browser:
            self._log_or_print("[错误] 浏览器未初始化。请先调用 initialize_browser()。", "error")
            return []

        order_status = order_status_override if order_status_override is not None else self.order_query_config.get("order_status", "买家已付款")
        product_id_to_filter = product_id_override.strip() if product_id_override is not None else self.order_query_config.get("product_id", "").strip()
        max_pages_to_scrape = max_pages_override if max_pages_override is not None else self.order_query_config.get("max_pages", 5)

        self._log_or_print(f"\n[抓取配置] 订单状态: '{order_status}', 商品ID: '{product_id_to_filter or '无'}', 最早时间: '{earliest_creation_time_str or '无'}', 最大页数: {max_pages_to_scrape}")

        earliest_dt = None
        if earliest_creation_time_str:
            try:
                earliest_dt = datetime.strptime(earliest_creation_time_str, "%Y-%m-%d %H:%M:%S")
            except ValueError:
                self._log_or_print(f"[警告] 最早订单创建时间格式无效: '{earliest_creation_time_str}'。将不按时间筛选。", "warning")
        
        all_orders_data = []
        stop_scraping_due_to_time = False

        try:
            if not self._search_conditions_set:
                # ====== 原有的“选择订单状态”代码块 ======
                msg = "尝试点击左侧导航栏的 '订单管理'..."
                self._log_or_print(msg)
                order_management_nav_locator = self.page.locator("ul.third-level-wrap--pliRWmI2 li:has-text('订单管理')").first
                try:
                    await order_management_nav_locator.wait_for(state="visible", timeout=10000)
                    # 改为JS点击，避免hover超时
                    msg = "使用JS方法点击'订单管理'..."
                    self._log_or_print(msg)
                    clicked = await self.page.evaluate("""
                        () => {
                            const nav = document.querySelector("ul.third-level-wrap--pliRWmI2 li");
                            const orderMgmt = Array.from(document.querySelectorAll("ul.third-level-wrap--pliRWmI2 li")).find(li => 
                                li.textContent && li.textContent.includes('订单管理')
                            );
                            if (orderMgmt) { 
                                orderMgmt.click(); 
                                return true; 
                            }
                            return false;
                        }
                    """)
                    if clicked:
                        msg = "'订单管理' JS点击成功。"
                        self._log_or_print(msg)
                        await self._random_async_delay("click")
                    else:
                        msg = "'订单管理' JS点击失败，尝试原方法..."
                        self._log_or_print(msg)
                        await order_management_nav_locator.hover(timeout=1500); await self._random_async_delay("hover")
                        await order_management_nav_locator.click(delay=random.uniform(40, 120))
                        self._log_or_print("'订单管理' 已点击。")
                        await self._random_async_delay("click")
                except Exception as e_nav_click:
                    msg = f"点击 '订单管理' 时出错: {e_nav_click}"
                    self._log_or_print(msg, "warning")
                    if await self._check_for_slider_captcha():
                        msg = "验证码处理后重试点击 '订单管理'..."; await order_management_nav_locator.click(); await self._random_async_delay("click")
                        self._log_or_print(msg)
                    else:
                        await self.page.screenshot(path="error_nav_click.png")
                        msg = "截图: error_nav_click.png"
                        self._log_or_print(msg)

                try: 
                    await self.page.wait_for_selector('input#orderStatus[role="combobox"]', timeout=5000)
                    msg = "订单状态选择器已找到。"
                    self._log_or_print(msg)
                except Exception as e_wait_el:
                    if await self._check_for_slider_captcha(): 
                        await self.page.wait_for_selector('input#orderStatus[role="combobox"]', timeout=15000)
                    else:
                        msg = f"等待 'input#orderStatus' 失败: {e_wait_el}"
                        self._log_or_print(msg, "warning")
                        await self.page.screenshot(path="error_initial_el_wait.png")
                        return []
                await self._random_async_delay("after_order_status_selector_wait") 

                self._log_or_print(f"尝试选择'{order_status}'...")
                dropdown_trigger_locator = self.page.locator('input#orderStatus[role="combobox"]')
                try:
                    # 改为JS点击下拉框
                    msg = "使用JS方法点击订单状态下拉框..."
                    self._log_or_print(msg)
                    dropdown_clicked = await self.page.evaluate("""
                        () => {
                            const dropdown = document.querySelector('input#orderStatus[role="combobox"]');
                            if (dropdown) { 
                                dropdown.click(); 
                                return true; 
                            }
                            return false;
                        }
                    """)
                    if dropdown_clicked:
                        await self._random_async_delay("after_dropdown_trigger_click")
                        msg = "下拉框JS点击成功，等待选项出现..."
                        self._log_or_print(msg)
                    else:
                        await dropdown_trigger_locator.hover(timeout=1500); await self._random_async_delay("hover")
                        await dropdown_trigger_locator.click(delay=random.uniform(40,120))
                        await self._random_async_delay("after_dropdown_trigger_click")
                    
                    if await self._check_for_slider_captcha(): 
                        await dropdown_trigger_locator.click(); 
                        await self._random_async_delay("after_dropdown_trigger_click") 
                    
                    menu_selector = "ul.next-menu.next-select-menu[role='listbox']"
                    await self.page.locator(menu_selector).wait_for(state="visible", timeout=7000)
                    
                    # 改为JS选择选项
                    msg = f"使用JS方法选择'{order_status}'选项..."
                    self._log_or_print(msg)
                    option_clicked = await self.page.evaluate(f"""
                        (status) => {{
                            const menu = document.querySelector("ul.next-menu.next-select-menu[role='listbox']");
                            if (menu) {{
                                const options = Array.from(menu.querySelectorAll("li[role='option']"));
                                const target = options.find(opt => 
                                    opt.getAttribute('title') === status || 
                                    (opt.textContent || opt.innerText || '').trim() === status
                                );
                                if (target) {{ 
                                    target.click(); 
                                    return true; 
                                }}
                            }}
                            return false;
                        }}
                    """, order_status)
                    
                    if option_clicked:
                        msg = f"成功选择'{order_status}'"
                        self._log_or_print(msg)
                        await self._random_async_delay("click")
                    else:
                        option_loc = self.page.locator(menu_selector).get_by_role("option", name=order_status)
                        await option_loc.wait_for(state="visible", timeout=5000)
                        await option_loc.hover(timeout=1000); await self._random_async_delay("hover")
                        await option_loc.click(delay=random.uniform(30,100))
                        self._log_or_print(f"成功选择'{order_status}'")
                except Exception as e_dropdown:
                    msg = f"选择'{order_status}'时出错: {e_dropdown}"
                    self._log_or_print(msg, "warning")
                    if await self._check_for_slider_captcha():
                        await dropdown_trigger_locator.click()
                        await self._random_async_delay("after_dropdown_trigger_click")
                        await self.page.locator(menu_selector).get_by_role("option", name=order_status).click()
                        self._log_or_print(f"重试选择'{order_status}'成功")
                    else:
                        msg = "尝试JS备选方法选择订单状态..."
                        self._log_or_print(msg)
                        await self.page.screenshot(path="error_dropdown.png")
                        try:
                            await self.page.evaluate(f'''
                                async (status) => {{
                                    document.querySelector('input#orderStatus[role="combobox"]').click();
                                    await new Promise(r => setTimeout(r, 500)); // Note: This inner sleep is not controlled by config
                                    const options = Array.from(document.querySelectorAll("ul.next-menu.next-select-menu[role='listbox'] li[role='option']"));
                                    const target = options.find(opt => (opt.getAttribute('title') === status || (opt.textContent || opt.innerText || '').trim() === status));
                                    if (target) target.click(); else console.error("JS Fallback: Option not found");
                                }}
                            ''', order_status)
                            self._log_or_print("JS备选方法执行完毕。")
                            await self._random_async_delay("after_js_dropdown_fallback") 
                        except Exception as e_js:
                            msg = f"JS备选方法失败: {e_js}"
                            self._log_or_print(msg, "warning")
                            return []
                await self._random_async_delay("after_order_status_selector_wait") 

                if product_id_to_filter:
                    self._log_or_print(f"尝试填入商品ID: {product_id_to_filter}...")
                    pid_input_loc = self.page.locator("input#auctionId")
                    try:
                        await pid_input_loc.wait_for(state="visible", timeout=5000)
                        await pid_input_loc.hover(timeout=1000); await self._random_async_delay("hover")
                        await pid_input_loc.click(delay=random.uniform(30,80)); await self._random_async_delay("input")
                        await pid_input_loc.fill(""); await self._random_async_delay("after_pid_clear") 
                        await pid_input_loc.fill(product_id_to_filter)
                        await self._random_async_delay("input")
                        self._log_or_print(f"商品ID '{product_id_to_filter}' 已填入。")
                    except Exception as e_pid:
                        msg = f"填入商品ID时出错: {e_pid}"
                        self._log_or_print(msg, "warning")
                        if await self._check_for_slider_captcha(): await pid_input_loc.fill(product_id_to_filter)
                        else: await self.page.screenshot(path="error_pid_input.png")
                # --- 新增：设置完筛选条件后点击“搜索订单”按钮 ---
                self._log_or_print("点击'搜索订单'按钮...")
                search_btn_loc = self.page.get_by_role("button", name="搜索订单")
                try:
                    await search_btn_loc.wait_for(state="visible", timeout=5000)
                    # 优先JS点击，兼容淘宝页面
                    self._log_or_print("使用JS方法点击'搜索订单'按钮...")
                    search_clicked = await self.page.evaluate("""
                        () => {
                            const buttons = Array.from(document.querySelectorAll('button'));
                            const searchBtn = buttons.find(btn => 
                                (btn.textContent || '').includes('搜索订单')
                            );
                            if (searchBtn) { 
                                searchBtn.click(); 
                                return true; 
                            }
                            return false;
                        }
                    """)
                    if search_clicked:
                        self._log_or_print("'搜索订单'JS点击成功。")
                        await self._random_async_delay("search_wait")
                    else:
                        await search_btn_loc.hover(timeout=1500); await self._random_async_delay("hover")
                        await search_btn_loc.click(delay=random.uniform(50,150))
                        self._log_or_print("'搜索订单'已点击。")
                        await self._random_async_delay("search_wait")
                        
                    if await self._check_for_slider_captcha(): 
                        await self._random_async_delay("after_search_captcha_handled") 
                except Exception as e_search:
                    self._log_or_print(f"点击'搜索订单'时出错: {e_search}", "warning")
                    # 可选：截图或重试
                    return []
                # --- 关键：首次设置筛选条件后再标记 ---
                self._search_conditions_set = True  # 标记已设置
            else:
                # 后续只需点击“搜索订单”按钮
                self._log_or_print("已设置过筛选条件，本次仅点击'搜索订单'刷新列表。")
                search_btn_loc = self.page.get_by_role("button", name="搜索订单")
                try:
                    await search_btn_loc.wait_for(state="visible", timeout=5000)
                    await search_btn_loc.click()
                    await self._random_async_delay("search_wait")
                except Exception as e_search:
                    self._log_or_print(f"点击'搜索订单'时出错: {e_search}", "warning")
                    # 可选：截图或重试
                    return []
            # ...后续处理订单列表的代码不变...
            order_list_item_selector = "div.next-table-body table.next-table-row"
            no_orders_msg_selector = "div.next-table-empty:has-text('没有符合条件的宝贝')"
            current_page_num = 1

            while current_page_num <= max_pages_to_scrape and not stop_scraping_due_to_time:
                self._log_or_print(f"\n--- 开始处理第 {current_page_num} 页 ---")
                found_orders_on_page = False
                try:
                    try:
                        await self.page.locator(no_orders_msg_selector).wait_for(state="visible", timeout=3000)
                        if await self.page.locator(no_orders_msg_selector).is_visible():
                            self._log_or_print("检测到提示：'没有符合条件的宝贝...'")
                            break
                    except Exception:
                        await self.page.wait_for_selector(order_list_item_selector, state="visible", timeout=22000)
                        self._log_or_print("订单列表项已加载。")
                        found_orders_on_page = True
                except Exception as e_load_orders:
                    if await self._check_for_slider_captcha():
                        try:
                            await self.page.wait_for_selector(order_list_item_selector, state="visible", timeout=20000)
                            found_orders_on_page = True
                        except Exception as e_retry_load:
                            self._log_or_print(f"重试等待订单列表失败: {e_retry_load}", "error")
                            break
                    else:
                        try:
                            await self.page.locator(no_orders_msg_selector).wait_for(state="visible", timeout=1000)
                            if await self.page.locator(no_orders_msg_selector).is_visible():
                                self._log_or_print("等待订单列表超时后检测到无订单提示。")
                        except Exception:
                            self._log_or_print(f"等待订单列表加载失败: {e_load_orders}", "error")
                            await self.page.screenshot(path=f"err_order_list_pg{current_page_num}.png")
                            break
                
                if not found_orders_on_page:
                    self._log_or_print(f"第 {current_page_num} 页未找到订单，停止。")
                    break
                await self._random_async_delay("process_order") 
                order_tables = await self.page.locator(order_list_item_selector).all()
                self._log_or_print(f"找到 {len(order_tables)} 个订单块。")
                if not order_tables and current_page_num > 1:
                    self._log_or_print("当前页未找到订单块，判断为最后一页。")
                    break
                
                for i, order_table_loc in enumerate(order_tables):
                    order_data = {"order_id": "N/A", "creation_time": "N/A", "product_name": "N/A", "buyer_account": "N/A", "unit_price": "N/A", "product_quantity": "N/A"}
                    try:
                        header_texts_locs = order_table_loc.locator(
                            "tr.next-table-group-header span.data-table-header--table-header-text--m_1KlFk")
                        if await header_texts_locs.count() >= 2:
                            oid_full = (await header_texts_locs.nth(0).text_content()) or ""
                            ctime_full = (await header_texts_locs.nth(1).text_content()) or ""
                            # 独立提取订单号
                            if "订单号：" in oid_full:
                                order_data["order_id"] = oid_full.split("订单号：", 1)[1].strip()
                            # 独立提取创建时间（注意 strip 掉前导空格）
                            text = ctime_full.strip()
                            if "创建时间：" in text or "成交时间：" in text:
                                prefix = "创建时间：" if "创建时间：" in text else "成交时间："
                                order_data["creation_time"] = text.split(prefix, 1)[1].strip()

                        buyer_nick_loc = order_table_loc.locator("tr.next-table-group-header span[id^='webww'] > span.ww-light + span")
                        try:
                            await buyer_nick_loc.wait_for(state="visible", timeout=500)
                            if await buyer_nick_loc.count() > 0 and await buyer_nick_loc.is_visible():
                                order_data["buyer_account"] = (await buyer_nick_loc.first.text_content() or "").strip()
                        except Exception:
                            # Backup for buyer nick
                            backup_buyer_loc = order_table_loc.locator("tr.next-table-group-header span:has-text('t**')").last
                            try:
                                await backup_buyer_loc.wait_for(state="visible", timeout=500)
                                if await backup_buyer_loc.is_visible():
                                    order_data["buyer_account"] = (await backup_buyer_loc.text_content() or "").strip()
                            except Exception:
                                self._log_or_print(f"  订单 {i+1}: 备用买家账号元素未找到或不可见。", "debug")

                        prod_name_loc = order_table_loc.locator("a.data-table-cell--sku-title--IS9X24Q")
                        try:
                            await prod_name_loc.wait_for(state="visible", timeout=500)
                            if await prod_name_loc.count() > 0 and await prod_name_loc.is_visible():
                                order_data["product_name"] = (await prod_name_loc.first.text_content() or "").strip()
                        except Exception:
                            pass
                        
                        price_loc = order_table_loc.locator("td[data-next-table-col='2'] div.next-table-cell-wrapper")
                        try:
                            await price_loc.wait_for(state="visible", timeout=500)
                            if await price_loc.count() > 0 and await price_loc.is_visible():
                                order_data["unit_price"] = (await price_loc.first.text_content() or "").strip().replace("￥", "")
                        except Exception:
                            pass
                        
                        quantity_loc = order_table_loc.locator("td[data-next-table-col='3'] div.next-table-cell-wrapper")
                        try:
                            await quantity_loc.wait_for(state="visible", timeout=500)
                            if await quantity_loc.count() > 0 and await quantity_loc.is_visible():
                                order_data["product_quantity"] = (await quantity_loc.first.text_content() or "").strip()
                        except Exception:
                            pass

                        self._log_or_print(f"  提取订单: ID={order_data['order_id']}, 时间={order_data['creation_time']}")
                        
                        if order_data["order_id"] == "N/A" or order_data["creation_time"] == "N/A":
                            self._log_or_print(f"  跳过无效订单 (ID 或 时间缺失)。", "debug")
                            continue

                        if earliest_dt and order_data["creation_time"] != "N/A":
                            try:
                                current_order_dt = datetime.strptime(order_data["creation_time"], "%Y-%m-%d %H:%M:%S")
                                if current_order_dt < earliest_dt:
                                    self._log_or_print(f"[时间筛选] 订单 {order_data['order_id']} ({order_data['creation_time']}) 早于 {earliest_creation_time_str}，停止。", "info")
                                    stop_scraping_due_to_time = True; break
                            except ValueError:
                                self._log_or_print(f"[警告] 无法解析订单 {order_data['order_id']} 的时间: {order_data['creation_time']}", "warning")
                        all_orders_data.append(order_data)
                        await self._random_async_delay("between_order_item_processing") 
                    except Exception as e_extract:
                        self._log_or_print(f"提取订单 {i+1} 数据时出错: {e_extract}", "error")
                        await self.page.screenshot(path=f"err_extract_pg{current_page_num}_item{i+1}.png")
                    if stop_scraping_due_to_time: break
                if stop_scraping_due_to_time: break

                if current_page_num >= max_pages_to_scrape:
                    self._log_or_print(f"已达最大翻页数 ({max_pages_to_scrape}页)。")
                    break
                
                self._log_or_print(f"尝试用JS点击下一页 (从 {current_page_num} 到 {current_page_num+1})...")
                clicked = await self.page.evaluate("""
                    () => {
                        const btn = document.querySelector("button.next-pagination-item.next-next:not([disabled])");
                        if (btn && !btn.disabled) { 
                            btn.click(); 
                            return true; 
                        }
                        return false;
                    }
                """)
                if clicked:
                    current_page_num += 1
                    msg = f"JS翻页成功，当前第 {current_page_num} 页"
                    self._log_or_print(msg)
                    await self._random_async_delay("navigation")
                    await self._close_popup_if_present()
                    if await self._check_for_slider_captcha():
                        self._log_or_print("翻页后出现验证码，已处理。")
                else:
                    msg = "JS翻页失败，可能已到末页，尝试原方法..."
                    self._log_or_print(msg, "warning")
                    next_page_btn_loc = self.page.locator("button.next-pagination-item.next-next:not([disabled])")
                    try:
                        if await next_page_btn_loc.count() > 0 and await next_page_btn_loc.is_enabled(timeout=2000):
                            msg = f"备用方法：准备点击下一页 (从 {current_page_num} 到 {current_page_num + 1})..."
                            self._log_or_print(msg)
                            await next_page_btn_loc.hover(timeout=1000); await self._random_async_delay("hover")
                            await next_page_btn_loc.click(delay=random.uniform(50,150)); current_page_num += 1
                            await self._random_async_delay("navigation"); await self._close_popup_if_present()
                            if await self._check_for_slider_captcha():
                                self._log_or_print("翻页后出现验证码，已处理。")
                        else: 
                            msg = "未找到可点击的'下一页'按钮，或按钮被禁用。判断为已到达最后一页。"
                            self._log_or_print(msg)
                            break
                    except Exception as e_next_page:
                        msg = f"备用翻页方法也失败: {e_next_page}"
                        self._log_or_print(msg, "warning")
                        await self.page.screenshot(path=f"err_next_page_pg{current_page_num}.png")
                        break
            
            if not all_orders_data:
                self._log_or_print("\n未提取到任何订单数据。")
            else:
                self._log_or_print(f"\n--- 本次抓取订单数据汇总 ({len(all_orders_data)}条) ---")
            return all_orders_data
        except Exception as e_scrape:
            self._log_or_print(f"抓取订单过程中发生严重错误: {e_scrape}", "error")
            traceback.print_exc()
            if self.page: await self.page.screenshot(path="error_scrape_critical.png")
            return all_orders_data

    async def close(self):
        """关闭浏览器和所有相关资源"""
        if self.page:
            try: 
                await self.page.close()
                self._log_or_print("页面已关闭。")
            except Exception as e: 
                self._log_or_print(f"关闭页面时出错: {e}", "warning")
            self.page = None
            
        if self.browser:
            try: 
                await self.browser.close()
                self._log_or_print("浏览器上下文已关闭。")
            except Exception as e: 
                self._log_or_print(f"关闭浏览器上下文时出错: {e}", "warning")
            self.browser = None
            
        if self.p:
            try: 
                await self.p.stop()
                self._log_or_print("Playwright 实例已停止。")
            except Exception as e: 
                self._log_or_print(f"停止 Playwright 时出错: {e}", "warning")
            self.p = None
            
        self._log_or_print("清理完成。")

async def standalone_main():
    scraper = OrderScraper()
    try:
        if not await scraper.initialize_browser():
            scraper._log_or_print("初始化失败，退出脚本。", "error")
            return

        scraper._log_or_print("\n--- 开始独立抓取任务 ---")
        # 示例：抓取特定时间之后的订单，您可以取消注释并修改时间
        # earliest_time = "2024-01-01 00:00:00" 
        # orders = await scraper.scrape_orders(earliest_creation_time_str=earliest_time)
        
        # 默认抓取（不按时间筛选，使用配置文件中的其他设置）
        orders = await scraper.scrape_orders()

        if orders:
            scraper._log_or_print(f"\n--- 独立抓取结果 ({len(orders)}条) ---")
            for idx, order in enumerate(orders):
                scraper._log_or_print(f"订单 {idx+1}: {order}")
        else:
            scraper._log_or_print("\n独立抓取未获取到订单数据。")
        await scraper._random_async_delay("standalone_end_wait") 
    except Exception as e:
        scraper._log_or_print(f"独立运行主函数发生错误: {e}", "error")
        traceback.print_exc()
    finally:
        await scraper.close()

if __name__ == "__main__":
    asyncio.run(standalone_main())
