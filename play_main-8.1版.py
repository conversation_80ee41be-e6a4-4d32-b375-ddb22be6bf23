import re
import sys
import time
import threading
import logging
import traceback
import queue
import yaml
from typing import Dict, List, Tuple, Any, Optional, cast
from multiprocessing import Process, Queue as MpQue<PERSON>, Manager  # 关键修改：添加 Manager 导入

# 导入模块
import play_init
from play_init import init_session, order_fetching_worker, monitor_main_loop_worker
import play_processing
from play_processing import process_messages_batch, handle_status_updates_worker
import Play_receiveMSG
import Play_db
import play_db_sync
import copy
import Play_rec_detect
from Play_obs import obs_controller
from play_db_sync import db_sync_manager
from Play_rec_detect import detection_receiver
from Play_move_client import MoveServiceClient
from Play_webdisplay import WebTextDisplay
from Play_virtual_player import VirtualPlayerManager

# --- 关键修改：将 config 和 config_lock 提升为全局变量 ---
config = {}
config_lock = threading.RLock() # 修正：使用 RLock 保持一致性
# --- 修改结束 ---

# 全局变量
logger = None
player_info = {}
player_comments = {}
player_games = {}
in_que = []

# 锁
queue_lock = threading.Lock()
player_info_lock = threading.RLock()
player_comments_lock = threading.RLock()
player_games_lock = threading.RLock()
current_player_lock = threading.RLock()

# 状态变量
# 删除这个易混淆的全局变量
# stop_flag = {'running': True} 
current_player = {}
status_update_event = queue.Queue()
# --- 关键修改：移除旧的GUI队列和客户端，使用新的多进程队列和进程对象 ---
mp_gui_queue: Optional[MpQueue] = None
gui_process: Optional[Process] = None
# --- 修改结束 ---
health_stopped = False
move_service_client: Optional[MoveServiceClient] = None
web_display_instance = None

# 添加线程变量，初始为None
game_thread = None
monitor_thread = None

# 订单系统
last_successful_order_fetch_newest_time_iso = None
order_fetching_thread = None
session_start_time_iso = None
virtual_player_manager_thread = None

# --- 关键修改：新增GUI进程启动包装函数 ---
def start_gui_process_wrapper(config, gui_queue, stop_flag):
    """
    一个简单的包装函数，用于在新的进程中启动GUI。
    这避免了在 play_main.py 中直接导入 play_displayer。
    """
    from play_displayer import start_displayer_process
    start_displayer_process(config, gui_queue, stop_flag)
# --- 修改结束 ---

# --- 关键修改：改造 fps_sender 以使用多进程队列 ---
def fps_sender(stop_flag: Dict[str, bool], detect_enabled: bool, 
               detection_receiver_instance: Any, mp_gui_queue: Optional[MpQueue], logger: Optional[logging.Logger]):
    """独立线程，定期发送FPS更新到GUI进程的多进程队列"""
    while True:
        try:
            # 将停止检查移入循环体并添加异常捕获
            if not stop_flag.get('running', True):
                if logger: logger.info("FPS发送线程：收到停止信号，线程退出。")
                break
        except (EOFError, BrokenPipeError):
            if logger: logger.info("FPS发送线程：共享状态连接已关闭，线程退出。")
            break
        
        if detect_enabled and detection_receiver_instance and mp_gui_queue:
            try:
                latest = detection_receiver_instance.get_latest_data()
                if latest:
                    now = time.time()
                    fps_data = {
                        'type': 'fps_update',
                        'fps': latest.get('fps', 0),
                        'data_age': now - latest.get('receive_time', 0)
                    }
                    mp_gui_queue.put(fps_data)
            except Exception as e:
                if logger: logger.warning(f"发送FPS更新时出错: {e}")
        
        # 每秒发送一次
        time.sleep(1.0)

def update_global_config(updated_sections: Dict[str, Any]):
    """线程安全地更新全局配置字典"""
    global config
    with config_lock:
        for section, new_values in updated_sections.items():
            if section in config:
                config[section].update(new_values)
            else:
                config[section] = new_values
        # --- 关键修改：使用更明确的检查，消除 Pylance 警告 ---
        if logger is not None:
            logger.info(f"[全局配置] 配置已更新，部分: {list(updated_sections.keys())}")

def main():
    """主函数：协调各个模块，运行主消息循环"""
    global logger, web_display_instance
    global game_thread, monitor_thread, move_service_client, order_fetching_thread
    global health_stopped, virtual_player_manager_thread
    global config, mp_gui_queue, gui_process

    try:
        # 1. 初始化配置和日志
        loaded_config = play_init.load_config()
        with config_lock:
            config.clear()
            config.update(loaded_config)
        
        logger = play_init.setup_logging(config)
        # --- 关键修改：使用更明确的检查，消除 Pylance 警告 ---
        if logger is not None:
            logger.info("应用程序启动")
        else:
            print("日志系统初始化失败")
            return

        # 创建一个可在进程间共享的字典
        manager = Manager()
        shared_stop_flag = manager.dict()  # 这个字典可以在进程间共享
        shared_stop_flag['running'] = True  # 初始化标志为 True
        
        # 在主线程中设置信号处理器
        play_init.setup_signal_handler(shared_stop_flag)
        if logger:
            logger.info("信号处理器已设置(使用共享状态字典)")
        
        # 2. 初始化WebTextDisplay进程
        web_display_instance = WebTextDisplay(config, shared_stop_flag)  # 使用共享状态字典
        if config.get('web_display', {}).get('enabled', False):
            web_display_started = web_display_instance.start()
            if logger:
                logger.info(f"Web显示进程启动{'成功' if web_display_started else '失败'}")
        
        # 3. 启动GUI进程（如果启用）
        if config.get('displayer', {}).get('enabled', False):
            # --- 关键修改：使用共享状态字典 ---
            mp_gui_queue = MpQueue()
            gui_process = Process(
                target=start_gui_process_wrapper,
                args=(config, mp_gui_queue, shared_stop_flag),  # 传入共享字典
                name="GUIProcess"
            )
            gui_process.start()
            if logger:
                logger.info(f"GUI 进程已通过 multiprocessing.Process 启动, PID: {gui_process.pid}")
            # --- 修改结束 ---
        
        # 4. 记录统一的免费游戏次数配置
        max_free_games = config.get('game', {}).get('max_free_games_per_session', 1)
        if logger:
            logger.info(f"每个会话最大免费游戏次数: {max_free_games}")

        # 5. 会话初始化
        session_id, obs_controller_instance, db_sync_manager_instance, is_new_session, session_start_time_iso = init_session(
            config=config, player_info=player_info, player_comments=player_comments, player_games=player_games,
            in_que=in_que, current_player=current_player, player_info_lock=player_info_lock,
            player_comments_lock=player_comments_lock, player_games_lock=player_games_lock, queue_lock=queue_lock
        )
        if logger:
            logger.info(f"会话初始化完成，{'新' if is_new_session else '已有'}会话，会话ID: {session_id}")

        # 确保数据库同步管理器有共享数据引用
        if db_sync_manager_instance:
            db_sync_manager_instance.shared_player_info = player_info
            db_sync_manager_instance.shared_player_comments = player_comments
            db_sync_manager_instance.shared_player_games = player_games
            db_sync_manager_instance.shared_in_que = in_que

        # 6. 启动各个线程
        
        # 启动移动服务客户端
        motormove_config = config.get('motormove_service', {})
        move_host = motormove_config.get('host', 'localhost')
        move_port = motormove_config.get('port', 5556)
        if move_host and move_port:
            move_service_client = MoveServiceClient(
                host=move_host,
                port=move_port,
                status_update_queue=status_update_event,
                stop_flag=shared_stop_flag  # 使用共享状态字典
            )
            if move_service_client:
                move_service_client.start()
            if logger:
                logger.info(f"移动服务客户端已启动，连接到: {move_host}:{move_port}")
        else:
            if logger:
                logger.error("移动服务配置不完整 (host/port)，程序无法正常运行")
            return

        # 定义物体检测更新回调
        def handle_object_detection_update(new_object_map: Dict[str, str], 
                                           all_detected_objects: List[Dict[str, Any]],
                                           status_queue: queue.Queue):
            """
            处理来自检测服务的物体映射和详细物体数据更新。
            此函数在 DetectionReceiver 线程中被调用。
            理想流程：只将事件放入队列，由 StatusUpdateThread 处理。
            """
            try:
                # 1. 将物体映射更新事件放入队列
                status_queue.put({
                    'type': 'object_map_updated',
                    'object_map': new_object_map,
                    'detection_objects': all_detected_objects
                })

            except Exception as e:
                if logger:
                    logger.error(f"在 handle_object_detection_update 回调中出错: {e}")

        # 启动检测数据接收器（仅在配置启用时）
        detect_enabled = config.get('Detect_server', {}).get('enabled', False)
        if detect_enabled:
            detection_receiver_instance = Play_rec_detect.start_detection_receiver(
                config, shared_stop_flag, handle_object_detection_update, status_update_event
            )
            if logger:
                logger.info("检测数据接收器线程已启动")
        else:
            detection_receiver_instance = None
            if logger:
                logger.info("检测功能未启用，跳过检测数据接收器启动")

        # 初始化虚拟玩家管理器
        vp_manager = None
        if config.get('virtual_player', {}).get('enabled', False):
            vp_manager = VirtualPlayerManager(
                config=config, stop_flag=shared_stop_flag,  # 使用共享状态字典
                player_info=player_info, player_comments=player_comments, player_games=player_games, in_que=in_que,
                player_info_lock=player_info_lock, player_comments_lock=player_comments_lock,
                player_games_lock=player_games_lock, queue_lock=queue_lock,
                status_update_queue=status_update_event,
                is_new_session=is_new_session  # 新增：传递会话状态
            )
            vp_manager.start()
            if vp_manager.thread and vp_manager.thread.is_alive():
                virtual_player_manager_thread = vp_manager.thread
                if logger:
                    logger.info("虚拟玩家管理器已启动")

        # 状态更新处理线程
        status_update_thread = threading.Thread(
            target=handle_status_updates_worker,
            args=(status_update_event, mp_gui_queue, shared_stop_flag, player_comments, player_games,  # 使用共享状态字典
                  player_info, current_player, config, db_sync_manager_instance,
                  player_comments_lock, player_games_lock, player_info_lock, move_service_client,
                  web_display_instance, vp_manager, update_global_config, queue_lock, in_que),
            daemon=True, name="StatusUpdateThread"
        )
        status_update_thread.start()
        if logger:
            logger.info("状态更新处理线程已启动")

        # FPS发送线程
        if config.get('displayer', {}).get('enabled', False):
            if detect_enabled:
                fps_sender_thread = threading.Thread(
                    target=fps_sender,
                    args=(shared_stop_flag, detect_enabled, detection_receiver_instance, mp_gui_queue, logger),  # 使用共享状态字典
                    daemon=True,
                    name="FPSSenderThread"
                )
                fps_sender_thread.start()
                logger.info("GUI的FPS发送线程已启动")

        # 订单抓取线程
        if config.get('orders', {}).get('enabled', False):
            order_fetching_thread = threading.Thread(
                target=order_fetching_worker,
                args=(config, session_id, session_start_time_iso,
                      player_info, player_info_lock, in_que, queue_lock, player_comments, player_games, 
                      player_comments_lock, player_games_lock, status_update_event, shared_stop_flag),  # 使用共享状态字典
                daemon=True, name="OrderFetchingThread"
            )
            order_fetching_thread.start()
            if logger:
                logger.info("订单抓取线程已启动")

        # 游戏处理线程
        game_thread = threading.Thread(
            target=play_processing.process_game_queue,
            args=(in_que, player_info, player_comments, player_games, queue_lock, shared_stop_flag, current_player,  # 使用共享状态字典
                  config, status_update_event, player_info_lock, player_comments_lock, player_games_lock,
                  move_service_client, vp_manager),
            daemon=True, name="GameProcessThread"
        )
        game_thread.start()
        if logger:
            logger.info("游戏处理线程已启动")

        # 主循环监控线程
        last_loop_time_ref = [time.time()]  # 使用列表以便引用传递
        monitor_thread = threading.Thread(
            target=monitor_main_loop_worker, 
            args=(shared_stop_flag, last_loop_time_ref),  # 使用共享状态字典
            daemon=True, name="MainLoopMonitor"
        )
        monitor_thread.start()
        if logger:
            logger.info("主循环监控线程已启动")

        # 7. 启动消息获取
        Play_receiveMSG.start_message_thread(
            config=config,
            target_url=f"http://{config.get('message_server', {}).get('host', '127.0.0.1')}:{config.get('message_server', {}).get('port', 9999)}{config.get('message_server', {}).get('route', '/game-da302d82')}", 
            interval=config.get('poll_interval', 1), 
            hb_interval=config.get('heartbeat_interval', 1),
            log_raw=True, filter_type=None
        )
        if logger:
            logger.info("消息获取后台线程已启动，开始接收消息...")

        # 8. 主消息处理循环
        if logger:
            logger.info("开始处理消息，按Ctrl+C停止...")
        last_priority_update = 0

        try:
            # 使用共享状态字典控制主循环
            while shared_stop_flag.get('running', True):
                last_loop_time_ref[0] = time.time()  # 更新监控时间戳
                
                # 处理消息
                try:
                    # 使用带超时的 get() 代替 get_nowait()
                    # 这使得主循环在没有消息时会阻塞等待，而不是空转
                    # 从而降低CPU使用率，并能更及时地响应停止信号
                    message_batch = Play_receiveMSG.message_queue.get(timeout=0.5)
                    Play_receiveMSG.message_queue.task_done()
                    process_messages_batch(
                        [message_batch], config, session_id, player_info, player_comments, player_games,
                        in_que, queue_lock, current_player, shared_stop_flag,
                        status_update_event, player_info_lock, player_comments_lock, player_games_lock,
                        vp_manager
                    )
                except queue.Empty:
                    # 超时后，继续循环，检查 stop_flag
                    pass

        # ...existing code...

        except KeyboardInterrupt:
            # --- 关键修改：使用更明确的检查，消除 Pylance 警告 ---
            if logger is not None:
                logger.info("检测到 Ctrl+C，准备退出程序...")
            shared_stop_flag['running'] = False  # 使用共享状态字典
            
    except (FileNotFoundError, yaml.YAMLError, KeyError) as e:
        error_msg = f"配置错误: {e}"
        # --- 关键修改：使用更明确的检查，消除 Pylance 警告 ---
        if logger is not None:
            logger.error(error_msg)
        else:
            print(error_msg)
        sys.exit(1)
    except Exception as e:
        error_msg = f"发生未预期的错误: {e}"
        # --- 关键修改：使用更明确的检查，消除 Pylance 警告 ---
        if logger is not None:
            logger.error(error_msg)
            logger.error(traceback.format_exc())
        else:
            print(error_msg)
            traceback.print_exc()
        sys.exit(1)
    finally:
        # --- 关键修改：使用更明确的检查，消除 Pylance 警告 ---
        if logger is not None:
            logger.info("进入清理阶段...")
        
        # 1. 统一设置停止标志
        # 使用共享状态字典通知所有线程/进程停止
        if 'shared_stop_flag' in locals():
            shared_stop_flag['running'] = False
        
        # 2. 调用统一的清理函数，并传入所有需要清理的资源
        import play_cleanup

        # 确保所有可能需要清理的局部变量都被正确获取
        local_obs_controller = locals().get('obs_controller_instance')
        local_db_sync_manager = locals().get('db_sync_manager_instance')
        local_detection_receiver = locals().get('detection_receiver_instance')
        
        play_cleanup.perform_cleanup(
            stop_flag=shared_stop_flag if 'shared_stop_flag' in locals() else {'running': False},
            obs_controller=local_obs_controller,
            db_sync_manager=local_db_sync_manager,
            queue_lock=queue_lock,
            in_que=in_que,
            game_thread=game_thread,
            monitor_thread=monitor_thread,
            health_stopped=health_stopped,
            order_fetching_thread=order_fetching_thread,
            detection_receiver=local_detection_receiver,
            move_service_client=move_service_client,
            web_display=web_display_instance,
            virtual_player_manager_thread=virtual_player_manager_thread,
            # --- 新增：将GUI进程和队列也交给cleanup函数处理 ---
            gui_process=gui_process,
            mp_gui_queue=mp_gui_queue
        )
        if 'manager' in locals():
            manager.shutdown()
        if logger:
            logger.info("程序退出")

if __name__ == "__main__":
    main()
