urls:
  # https://live.douyin.com/865327074335
  - https://live.douyin.com/875402591878
  - https://live.douyin.com/504448096643
  - https://tbzb.taobao.com/live?spm=a21bo.29164009.discovery.1.159f5f7eWCWMuW&liveSource=pc_live.discovery&liveId=517048387597

# 浏览器设置
browser:
  headless: true  # 设置为true时浏览器在后台运行(无头模式)，false时显示浏览器界面

# 评论缓冲区设置
comment_buffer:
  max_buffer_size: 1   # 降低为1条评论就写入一次文件，进一步减少延迟
  flush_interval: 1    # 降低为1秒自动刷新一次缓冲区，减少延迟
  output_file: 'getmsg.txt'  # 输出文件路径

# 消息服务器设置
message_server:
  enabled: true        # 是否启用消息服务器
  host: '127.0.0.1'    # 服务器地址
  port: 9999           # 服务器端口
  route: '/game-da302d82'  # URL路由
  push_mode: false      # 不用主动推送模式，否则与三方工具不兼容

# 轮询请求设置
poll_interval: 1          # 轮询间隔时间(秒)
heartbeat_interval: 1     # 心跳发送间隔时间(秒)
raw_log_file: 'receive_raw.log'  # 原始响应日志文件
record_all_message_types: true   # 是否记录所有类型消息