# 导入所需模块
import requests  # 用于发送HTTP请求
import time      # 用于时间控制和格式化
import json      # 用于格式化JSON数据
import os        # 用于文件路径操作
import sys       # 用于获取执行路径
import yaml      # 用于读取YAML配置文件
import logging   # 用于日志记录
from typing import Generator, Dict, Any, Optional
from Play_request_guard import safe_get, request_guard  # 导入请求守护功能
import threading
import queue  # 添加queue导入

def get_local_path(filename):
    """获取本地文件路径，支持打包后的exe和脚本运行"""
    # 检测是否为编译后的程序
    is_compiled = False

    # 多种检测方法
    if (hasattr(sys, '_MEIPASS') or  # PyInstaller
        getattr(sys, 'frozen', False) or  # 标准frozen检测
        sys.argv[0].endswith('.exe') or  # exe文件
        os.path.abspath(sys.executable) == os.path.abspath(sys.argv[0])):  # executable等于argv[0]
        is_compiled = True

    # 额外检查：如果__file__在临时目录，也认为是编译后的
    if not is_compiled and '__file__' in globals():
        file_path = os.path.abspath(__file__)
        if 'temp' in file_path.lower() or 'onefil' in file_path.lower():
            is_compiled = True

    if is_compiled:
        # 使用sys.executable的目录
        base_path = os.path.dirname(os.path.abspath(sys.executable))
    else:
        # 脚本模式，使用__file__
        base_path = os.path.dirname(os.path.abspath(__file__))

    return os.path.join(base_path, filename)

# 配置日志
logger = logging.getLogger('poll_request')
logger.setLevel(logging.INFO)
if not logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - [%(name)s] - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

# 全局配置变量，由外部传入
_config = None

def set_config(config):
    """设置配置，由外部调用"""
    global _config
    _config = config

def get_config():
    """获取配置，内部使用"""
    global _config
    if _config is None:
        # 兜底方案：如果没有外部配置，使用默认配置
        return {
            'message_server': {'host': '127.0.0.1', 'port': 9999, 'route': '/game-da302d82'},
            'poll_interval': 1,
            'heartbeat_interval': 1,
            'raw_log_file': 'receive_raw.log',
            'record_all_message_types': True
        }
    return _config

# 新增全局队列和线程控制变量
message_queue = queue.Queue()
_message_thread = None
_message_thread_stop = False

def log_receive_raw(data):
    """
    记录原始响应数据到日志文件
    """
    config = get_config()
    raw_log_file = config.get('raw_log_file', 'receive_raw.log')
    raw_log_path = get_local_path(raw_log_file)
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    with open(raw_log_path, "a", encoding="utf-8") as f:
        f.write(f"===== 响应时间: {timestamp} =====\n")
        f.write(json.dumps(data, ensure_ascii=False, indent=2))
        f.write("\n\n")

def get_messages_stream(target_url: Optional[str] = None, 
                        interval: Optional[float] = None, 
                        heartbeat_interval: Optional[float] = None,
                        log_raw: bool = True,
                        filter_type: Optional[str] = None) -> Generator[Dict[str, Any], None, None]:
    """
    生成器函数，不断获取消息并实时返回
    
    参数:
    - target_url: API请求地址，如果为None则从配置获取
    - interval: 请求间隔时间(秒)，如果为None则从配置获取
    - heartbeat_interval: 心跳间隔时间(秒)，如果为None则从配置获取
    - log_raw: 是否记录原始响应
    - filter_type: 仅返回指定类型的消息，None表示返回所有消息
    
    返回:
    - 生成器，每次yield一条消息
    """
    config = get_config()
    
    # 使用传入参数或从配置获取默认值 - 修复：确保参数不为None
    if target_url is None:
        server_config = config.get('message_server', {})
        target_url = f"http://{server_config.get('host', '127.0.0.1')}:{server_config.get('port', 9999)}{server_config.get('route', '/game-da302d82')}"
    
    if interval is None:
        interval = config.get('poll_interval', 1)
    
    if heartbeat_interval is None:
        heartbeat_interval = config.get('heartbeat_interval', 1)
    
    # 确保所有参数都不为None
    target_url = target_url or "http://127.0.0.1:9999/game-da302d82"
    interval = float(interval) if interval is not None else 1.0
    heartbeat_interval = float(heartbeat_interval) if heartbeat_interval is not None else 1.0
    
    message_types_count = {}
    last_message_time = time.time()  # 用于避免重复消息的时间戳
    last_heartbeat_time = time.time()  # 记录上次发送心跳的时间
    processed_msg_ids = set()  # 记录已处理过的消息ID，防止重复处理
    
    # 启动请求守护健康检查 - 修复：确保target_url不为None
    logger.debug("[消息流] 启动健康检查")
    if target_url:
        request_guard.start_health_check(target_url)
    
    try:
        while True:
            logger.debug("[消息流] 循环开始")
            current_time = time.time()
            received_messages = False  # 标记是否在本次循环收到了消息
            
            try:
                # 使用安全的GET请求，有超时和重试机制 - 修复：确保target_url不为None
                logger.debug(f"[消息流] 调用 safe_get 请求 URL: {target_url}")
                if target_url:
                    response = safe_get(target_url, timeout=5)
                else:
                    logger.error("[消息流] target_url 为空，跳过请求")
                    response = None
                logger.debug(f"[消息流] safe_get 返回: {'Response object' if response else 'None'}, status: {response.status_code if response else 'N/A'}")
                
                # 处理成功响应
                if response and response.status_code == 200:
                    # 解析响应内容
                    try:
                        logger.debug("[消息流] 尝试解析 JSON 响应")
                        data = response.json()
                        logger.debug("[消息流] JSON 解析成功")
                        
                        # 记录原始响应
                        if log_raw:
                            log_receive_raw(data)
                        
                        # 按原样把整包数据 yield 给上层，让批量分支能看到 "message": [...]
                        # 这是唯一保留的 yield，移除了后面对单条消息的 yield
                        yield data
                        
                        # 处理消息数组，仅用于判断是否收到消息
                        has_messages = False
                        
                        if "message" in data and isinstance(data["message"], list):
                            logger.debug(f"[消息流] 响应中包含 {len(data['message'])} 条消息")
                            for message in data["message"]:
                                logger.debug(f"[消息流] 处理消息: {message.get('type', 'Unknown')}, ID: {message.get('msg_id', 'N/A')}")
                                # 如果message只有plat字段，跳过
                                if len(message) == 1 and "plat" in message:
                                    logger.debug("[消息流] 跳过仅含 plat 字段的消息")
                                    continue
                                    
                                msg_type = message.get("type", "Unknown")
                                msg_id = message.get("msg_id", "")
                                
                                # 如果这条消息已经处理过，跳过
                                if msg_id and msg_id in processed_msg_ids:
                                    logger.debug(f"[消息流] 跳过已处理的消息 ID: {msg_id}")
                                    continue
                                
                                # 记录消息ID为已处理
                                if msg_id:
                                    logger.debug(f"[消息流] 记录消息 ID 为已处理: {msg_id}")
                                    processed_msg_ids.add(msg_id)
                                
                                # 更新消息类型统计
                                if msg_type in message_types_count:
                                    message_types_count[msg_type] += 1
                                else:
                                    message_types_count[msg_type] = 1
                                
                                # 不再单独yield每个消息
                                # 原代码: if filter_type is None or msg_type == filter_type:
                                #            yield message
                                
                                has_messages = True
                                received_messages = True
                            
                            # 如果这次请求获取到了消息，减少等待时间再次请求
                            if has_messages and len(data["message"]) > 0:
                                logger.debug("[消息流] 本次请求收到有效消息，短暂休眠 0.1 秒后继续")
                                time.sleep(0.1)  # 短暂等待后立即请求
                                continue
                        else:
                             logger.debug("[消息流] 响应中无 'message' 列表或格式不正确")
                    except ValueError:
                        logger.error(f"错误: 无效的JSON响应。原始内容: {response.text}")
                else:
                    # 处理错误状态码
                    status_code = response.status_code if response else "无响应"
                    logger.error(f"错误: 收到状态码 {status_code}")
                    
            except Exception as e:
                # 处理请求异常
                logger.error(f"请求失败: {e}")
            
            # 检查是否需要发送心跳
            time_since_heartbeat = current_time - last_heartbeat_time
            logger.debug(f"[消息流] 检查心跳: 未收到消息={not received_messages}, 距离上次心跳={time_since_heartbeat:.1f}s, 心跳间隔={heartbeat_interval}s")
            if not received_messages and (time_since_heartbeat >= heartbeat_interval):
                # 创建并发送心跳消息
                heartbeat_message = {
                    "type": "Heartbeat",
                    "msg_id": f"heartbeat_{int(current_time * 1000)}",
                    "timestamp": int(current_time * 1000),
                    "content": "heartbeat"
                }
                logger.debug("[消息流] Yield 心跳消息")
                yield heartbeat_message
                last_heartbeat_time = current_time
            
            # 等待指定间隔时间
            logger.debug(f"[消息流] 休眠 {interval} 秒")
            time.sleep(interval)
            logger.debug("[消息流] 休眠结束")
            
            # 定期清理过旧的消息ID，防止集合无限增长
            if len(processed_msg_ids) > 10000:
                logger.debug(f"[消息流] 清理旧消息 ID，当前数量: {len(processed_msg_ids)}")
                processed_msg_ids = set(list(processed_msg_ids)[-5000:])
                logger.debug(f"[消息流] 清理后数量: {len(processed_msg_ids)}")
            logger.debug("[消息流] 循环结束")
                
    except KeyboardInterrupt:
        # 捕获键盘中断
        logger.info("\n接收到终止信号，停止获取消息")
        if message_types_count:
            logger.info("\n消息类型统计:")
            for msg_type, count in message_types_count.items():
                logger.info(f"  - {msg_type}: {count} 条")
        return
    finally:
        # 停止健康检查
        logger.debug("[消息流] 停止健康检查")
        request_guard.stop_health_check()

def _message_producer(target_url, interval, hb_interval, log_raw, filter_type):
    """消息生产者函数，运行在后台线程中，获取消息并放入队列"""
    global _message_thread_stop
    logger.debug("[消息线程] 开始运行消息获取线程")
    
    try:
        gen = get_messages_stream(
            target_url=target_url, 
            interval=interval, 
            heartbeat_interval=hb_interval,
            log_raw=log_raw, 
            filter_type=filter_type
        )
        
        while not _message_thread_stop:
            try:
                msg = next(gen)
                message_queue.put(msg)
                logger.debug(f"[消息线程] 获取到消息，类型待判断")
            except StopIteration:
                logger.info("[消息线程] 消息生成器结束")
                break
            except Exception as e:
                logger.error(f"[消息线程] 获取消息出错: {e}")
                time.sleep(0.1)  # 出错后短暂休眠
    except Exception as e:
        logger.error(f"[消息线程] 消息生产者函数发生未捕获异常: {e}")
    finally:
        logger.debug("[消息线程] 消息获取线程结束")

def start_message_thread(config=None, target_url: Optional[str]=None, interval: Optional[float]=None, 
                         hb_interval: Optional[float]=None, log_raw: bool=True, filter_type: Optional[str]=None):
    """启动后台消息获取线程"""
    global _message_thread, _message_thread_stop
    
    # 设置配置
    if config:
        set_config(config)
    
    # 如果线程已存在且正在运行，直接返回
    if _message_thread and _message_thread.is_alive():
        logger.debug("[消息线程] 消息线程已在运行")
        return
    
    # 从配置获取默认值 - 修复：确保参数不为None
    current_config = get_config()
    if target_url is None:
        server_config = current_config.get('message_server', {})
        target_url = f"http://{server_config.get('host', '127.0.0.1')}:{server_config.get('port', 9999)}{server_config.get('route', '/game-da302d82')}"
    
    if interval is None:
        interval = current_config.get('poll_interval', 1)
    
    if hb_interval is None:
        hb_interval = current_config.get('heartbeat_interval', 1)
    
    # 确保所有参数都不为None
    target_url = target_url or "http://127.0.0.1:9999/game-da302d82"
    interval = float(interval) if interval is not None else 1.0
    hb_interval = float(hb_interval) if hb_interval is not None else 1.0
    
    logger.info("[消息线程] 启动消息获取后台线程")
    _message_thread_stop = False
    _message_thread = threading.Thread(
        target=_message_producer,
        args=(target_url, interval, hb_interval, log_raw, filter_type),
        daemon=True,
        name="MessagePollThread"
    )
    _message_thread.start()
    logger.info("[消息线程] 消息获取线程已启动")

def stop_message_thread():
    """停止后台消息获取线程"""
    global _message_thread_stop
    
    logger.debug("[消息线程] 尝试停止消息线程")
    _message_thread_stop = True
    
    if _message_thread:
        logger.debug("[消息线程] 等待消息线程结束")
        _message_thread.join(timeout=2)
        if _message_thread.is_alive():
            logger.warning("[消息线程] 消息线程未在2秒内结束")
        else:
            logger.info("[消息线程] 消息线程已停止")
    
    # 清空消息队列
    while not message_queue.empty():
        try:
            message_queue.get_nowait()
            message_queue.task_done()
        except:
            break
    
    logger.debug("[消息线程] 消息队列已清空")

def poll_request():
    # 主轮询函数，从消息队列中获取消息并处理，直到按下Ctrl+C停止
    config = get_config()
    record_all_message_types = config.get('record_all_message_types', True)
    raw_log_file = config.get('raw_log_file', 'receive_raw.log')
    heartbeat_interval = config.get('heartbeat_interval', 1)
    
    # 创建一个统计不同消息类型的字典
    message_types_count = {}
    
    print(f"记录所有消息类型: {'是' if record_all_message_types else '否'}")
    print(f"原始响应将被记录到: {os.path.abspath(raw_log_file)}")
    print(f"心跳间隔: {heartbeat_interval}秒")
    print("开始轮询消息... 按 Ctrl+C 停止")
    
    try:
        # 启动后台消息获取线程
        start_message_thread(filter_type=None if record_all_message_types else "ChatMessage")
        
        while True:
            try:
                # 从队列中获取消息，非阻塞
                message = message_queue.get_nowait()
                msg_type = message.get("type", "Unknown")
                
                # 统计消息类型
                if msg_type in message_types_count:
                    message_types_count[msg_type] += 1
                else:
                    message_types_count[msg_type] = 1
                
                # 区别处理心跳消息和普通消息
                if msg_type == "Heartbeat":
                    print(".", end='', flush=True)  # 只打印一个点表示心跳
                else:
                    print(f"\n处理 {msg_type} 类型消息: {json.dumps(message, ensure_ascii=False)}")
                    print("轮询中... 按 Ctrl+C 停止", end='\r')
                    
            except queue.Empty:
                # 队列为空，短暂休眠后继续
                time.sleep(0.1)
                
    except KeyboardInterrupt:
        print("\n收到 Ctrl+C，正在停止...")
    finally:
        # 停止后台消息获取线程
        stop_message_thread()
    
    # 打印消息类型统计
    if message_types_count:
        print("\n收到的消息类型统计:")
        for msg_type, count in message_types_count.items():
            print(f"  - {msg_type}: {count} 条")

if __name__ == "__main__":
    # 程序入口，启动轮询
    poll_request()
